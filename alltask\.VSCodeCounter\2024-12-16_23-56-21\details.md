# Details

Date : 2024-12-16 23:56:21

Directory d:\\PROJECT\\CASC502\\4-Float_Code\\alltask

Total : 20 files,  5583 codes, 336 comments, 385 blanks, all 6304 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000000.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000000.json) | JSON | 198 | 0 | 0 | 198 |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000001.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000001.json) | JSON | 198 | 0 | 0 | 198 |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000002.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000002.json) | JSON | 198 | 0 | 0 | 198 |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000003.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000003.json) | JSON | 198 | 0 | 0 | 198 |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000004.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000004.json) | JSON | 198 | 0 | 0 | 198 |
| [Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000005.json](/Eutelsat-117_data/json/Eutelsat117WestB_c1_5km_L0_180_000005.json) | JSON | 198 | 0 | 0 | 198 |
| [bbox_3mdl1219_scaled.json](/bbox_3mdl1219_scaled.json) | JSON | 254 | 0 | 0 | 254 |
| [bbox_5mdl_scaled_pad.json](/bbox_5mdl_scaled_pad.json) | JSON | 2,522 | 0 | 0 | 2,522 |
| [camera_K.json](/camera_K.json) | JSON | 1 | 0 | 0 | 1 |
| [inference_pth_est_20240417.py](/inference_pth_est_20240417.py) | Python | 65 | 8 | 23 | 96 |
| [onnx_inference_240418.py](/onnx_inference_240418.py) | Python | 174 | 40 | 61 | 275 |
| [onnx_inference_alltask_float.py](/onnx_inference_alltask_float.py) | Python | 153 | 37 | 42 | 232 |
| [onnx_inference_alltask_quant.py](/onnx_inference_alltask_quant.py) | Python | 149 | 39 | 45 | 233 |
| [onnx_inference_getoutput.py](/onnx_inference_getoutput.py) | Python | 134 | 13 | 24 | 171 |
| [pose.py](/pose.py) | Python | 75 | 4 | 17 | 96 |
| [pose_vis.py](/pose_vis.py) | Python | 147 | 5 | 22 | 174 |
| [seg_utils.py](/seg_utils.py) | Python | 60 | 118 | 29 | 207 |
| [seg_vis.py](/seg_vis.py) | Python | 62 | 3 | 17 | 82 |
| [tools/visualize.py](/tools/visualize.py) | Python | 462 | 17 | 76 | 555 |
| [yolov5_utils.py](/yolov5_utils.py) | Python | 137 | 52 | 29 | 218 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)