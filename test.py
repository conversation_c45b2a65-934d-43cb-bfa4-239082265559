import onnx
import numpy as np
from onnx import helper, TensorProto

# 输入输出定义
A = helper.make_tensor_value_info('A', TensorProto.INT8, ['N'])
B = helper.make_tensor_value_info('B', TensorProto.INT8, ['N'])
C = helper.make_tensor_value_info('C', TensorProto.INT8, ['N'])

# 量化参数
A_scale = helper.make_tensor('A_scale', TensorProto.FLOAT, [], [np.float32(0.025456994771957397)])
A_zero_point = helper.make_tensor('A_zero_point', TensorProto.INT8, [], [-106])

B_scale = helper.make_tensor('B_scale', TensorProto.FLOAT, [], [np.float32(0.035809919238090515)])
B_zero_point = helper.make_tensor('B_zero_point', TensorProto.INT8, [], [-120])

C_scale = helper.make_tensor('C_scale', TensorProto.FLOAT, [], [np.float32(0.0365859717130661)])
C_zero_point = helper.make_tensor('C_zero_point', TensorProto.INT8, [], [-105])

# 使用 QLinearAdd 节点
qlinear_add_node = helper.make_node(
    'QLinearAdd',
    inputs=['A', 'A_scale', 'A_zero_point',
            'B', 'B_scale', 'B_zero_point',
            'C_scale', 'C_zero_point'],
    outputs=['C']
)

# 构建模型（使用 opset 10）
graph_def = helper.make_graph(
    [qlinear_add_node],
    'qlinear_add_graph',
    [A, B],
    [C],
    initializer=[
        A_scale, A_zero_point,
        B_scale, B_zero_point,
        C_scale, C_zero_point
    ]
)

model_def = helper.make_model(
    graph_def,
    producer_name='onnx-qlinearadd',
    opset_imports=[helper.make_opsetid("", 10)]  # 使用 opset 10
)

# 保存模型
onnx.save(model_def, 'qlinear_add.onnx')
print("模型已保存为 qlinear_add.onnx")
