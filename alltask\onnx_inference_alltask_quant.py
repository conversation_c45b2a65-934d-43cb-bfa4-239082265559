import os, time
import numpy as np
from PIL import Image
from seg_utils import rawOutput2SegMap, segMapResize, componentFeature, calRealSize, imageResize
from yolov5_utils import filter_box, im_crop, draw_det_box
from seg_vis import draw_points, save_results, draw_seg_box
import onnxruntime
from pathlib import Path
import cv2
import json
from pose_vis import draw_pose_result



os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"
           

def get_calibration_data():
# def get_calibration_data(yolov5_cdata):
# def get_calibration_data(model_name):
    # add by luis
    crop_resize_img_pose_all = []
    sat_iden_all = []
    crop_resize_img_rec_all = []
    # yolov5_sate_gray_640
    img_all = []
    crop_resize_img_com_all = []

    # 参数设置
    det_sate_input_size = 640  # 整星检测输入尺寸
    det_component_input_size=640  # 部件检测输入尺寸
    seg_input_size = 576  # 部件分割输入尺寸
    reg_identity_input_size=640  # 身份识别输入尺寸
    pose_estimation_size = 512   # 姿态估计输入尺寸

    base_save_path = './log/im_result_alltask_quant'
    os.makedirs(base_save_path, exist_ok=True)


    key_points_3d_dir ='./bbox_5mdl_scaled_pad.json'
    camera_K_dir  ='./camera_K.json'

    CLASSES_sate = ['satellite']
    CLASSES_component = ['panel', 'aerial', 'nozzle', 'optical', 'holder', 'sensor', 'docking', 'main', 'arm', 'load',
                         'aerial-pole', 'aerial-unknown', 'others']

    COLOR_sate =[(255,0, 0)]

    COLOR_component = [(255,0,0), (0,255,0),(255,255,0),(0,0,255),(255,0,255),(0,0,124),(202,202,202),(255,255,255),
                       (124,0,0),(0,124,0),(124,124,0),(0,124,124),(0,255,255)]


    # 定义模型
    # quant model
    det_sate_model_quant_before = onnxruntime.InferenceSession('./yolov5_satellite_detection_before_quant_int8_int8.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider']);
    det_sate_model_after = onnxruntime.InferenceSession('./satellite_detection_after.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider']);

    det_component_model_quant_before = onnxruntime.InferenceSession('./yolov5_component_detection_before_quant_int8_int8.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
    det_component_model_after = onnxruntime.InferenceSession('./yolov5_component_detection_after.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])

    reg_identity_model_quant = onnxruntime.InferenceSession('./mobilenet_quant_i8i8.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])



    pose_model = onnxruntime.InferenceSession('./end2end0117.onnx', providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])

    # 读取测试数据
    data_base_path = './Eutelsat-117_data'
    data_names = os.listdir(os.path.join(data_base_path, 'src'))

    im_ids = []
    images = []
    labels = []
    for ii, line in enumerate(data_names):
        _image = os.path.join(data_base_path, 'src', line)
        # _cat = os.path.join(data_base_path, 'label', line)
        assert os.path.isfile(_image)
        # assert os.path.isfile(_cat)

        im_ids.append(str(Path(line).stem))
        images.append(_image)
        # labels.append(_cat)

    results = {'sate_coord': [], 'sate_id': {}, 'sate_comp': [], 'sate_pose': {}}

    for image, im_id in zip(images, im_ids):
        # st_time = time.time()
        # -----------------------图片读取与预处理-----------------------------
        # 读取图片和分割标签
        _img = Image.open(image).convert('L')  # 单通道
        # _target = Image.open(label)  # 分割图

        img_gray = np.array(_img).astype(np.uint8)

        # 图片尺寸变换
        img = cv2.resize(img_gray.astype(np.float32), (det_sate_input_size, det_sate_input_size))
        # 图片像素归一化
        img /= 255.0
        # 图片通道扩展
        img = np.expand_dims(img, axis=0)
        img = np.expand_dims(img, axis=0)

        # ---------------------------------------------------------------
        ### ********************************
        # onnx model quant
        # if (model_name == 'yolov5_sate_gray_640'):
        ### ********************************
        # -------------------------------整星检测--------------------------
        if im_id == 'GSSAP_c1_d0_L0_000001':
            img.tofile('img01.bin')
        pred_before_component = det_sate_model_quant_before.run(['269', '307', '345'], {'images': img})
        pred = det_sate_model_after.run(['output'], {'269': pred_before_component[0], '307': pred_before_component[1], '345': pred_before_component[2]})[0]
        # pred = yolov5_cdata
        # 整星边界框NMS过滤与合并
        outbox = filter_box(pred, 0.5, 0.5)

        if outbox.shape[0] > 0:
            # ------------根据检测整星结果对目标所在区域进行图像切片-----------
            rate_w = img_gray.shape[-1] / img.shape[-1]
            rate_h = img_gray.shape[-2] / img.shape[-2]
            crop_coor = im_crop(outbox, [img_gray.shape[-2], img_gray.shape[-1]], rate_h, rate_w)
            img_crop_gray = img_gray[crop_coor[1]:crop_coor[3]+1, crop_coor[0]:crop_coor[2]+1]
            crop_size = [img_crop_gray.shape[-1], img_crop_gray.shape[-2]]
            results['sate_coord'].append(crop_coor)
            # 保存整星切片图像，用于验证，实际不需要
            cv2.imwrite(os.path.join(base_save_path, '{}_crop.png'.format(im_id)), img_crop_gray)
            img_sate_det_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
            img_sate_det_vis = draw_det_box(img_sate_det_vis, outbox, CLASSES_sate, COLOR_sate, rate_h, rate_w)
            cv2.imwrite(os.path.join(base_save_path, '{}_sate_det_vis.png'.format(im_id)), img_sate_det_vis)


            # -----------------------身份识别-----------------------------------------
            crop_resize_img_rec = cv2.resize(img_crop_gray.astype(np.float32), (reg_identity_input_size, reg_identity_input_size))
            crop_resize_img_rec /= 255
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)
            
            output_class = reg_identity_model_quant.run(['output_cls'], {'input': crop_resize_img_rec})[0]

            output_class_real_c = output_class[:, 0:19]
            output_class_real_c_exp = np.exp(output_class_real_c)
            output_class_real_c_exp = output_class_real_c_exp / np.sum(output_class_real_c_exp, axis=1, keepdims=True)
            output_class_temp = output_class_real_c_exp[:, 0:5]
            pred_class = np.argmax(output_class_temp, axis=1)
            pred_class_p = np.max(output_class_temp, axis=1)
            results['sate_id']['id'] = pred_class
            results['sate_id']['conf'] = pred_class_p
            print(results['sate_id']['id'])
            print(results['sate_id']['conf'])


            # -----------------------部件检测----------------------------------------
            crop_resize_img_com = cv2.resize(img_crop_gray.astype(np.float32), (det_component_input_size, det_component_input_size))
            crop_resize_img_com /= 255
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            # if im_id == 'GSSAP_c1_d0_L0_000019':
            #     crop_resize_img_com.tofile('img03.bin')
            # 图像输入网络
            pred_before = det_component_model_quant_before.run(['269', '307', '345'], {'images': crop_resize_img_com})
            pred_com = det_component_model_after.run(['output'], {'269': pred_before[0], '307': pred_before[1], '345': pred_before[2]})[0]

     
            # NMS
            outbox_com = filter_box(pred_com, 0.5, 0.5)
            print(outbox_com)
            if outbox_com.shape[0] > 0:
                results['sate_comp'].append(outbox_com)

                # 可视化，用于效果验证，实际运行不需要
                rate_w = img_crop_gray.shape[-1] / crop_resize_img_com.shape[-1]
                rate_h = img_crop_gray.shape[-2] / crop_resize_img_com.shape[-2]
                img_com_det_vis = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
                img_com_det_vis = draw_det_box(img_com_det_vis, outbox_com, CLASSES_component, COLOR_component, rate_h, rate_w)
                cv2.imwrite(os.path.join(base_save_path, '{}_com_det_vis.png'.format(im_id)), img_com_det_vis)
            else:
                print('No component detected.')
                
            # -----------------------相对姿态估计--------------------------------------------
            with open(key_points_3d_dir, 'r') as f:
                points_3d_lib = json.load(f)
            with open(camera_K_dir, 'r') as f:
                camera_K = json.load(f)
            crop_resize_img_pose = cv2.resize(img_crop_gray.astype(np.float32), (pose_estimation_size, pose_estimation_size))
            crop_resize_img_pose /= 255
            crop_resize_img_pose = np.stack([crop_resize_img_pose, crop_resize_img_pose, crop_resize_img_pose], axis=0)
            # crop_resize_img_pose = np.expand_dims(crop_resize_img_pose, axis=0)
            crop_resize_img_pose = np.expand_dims(crop_resize_img_pose, axis=0)
            pred_class_pose = pred_class[0]
            pred_class_id = pred_class_pose + 1
            sat_id = 2 * pred_class_id - 2
            cam_id = 2 * pred_class_id - 1
            sat_iden = np.array([sat_id], dtype=np.int64)
            pose_result = pose_model.run(['sat_keypoints_2d', 'cam_keypoints_2d'], {'img': crop_resize_img_pose, 'img_metas': sat_iden})
            sat_points_2d = pose_result[0]
            cam_points_2d = pose_result[1]
            if sat_points_2d.shape[0] > 0:
                # pnp 解算姿态
                n = sat_points_2d.shape[0]
                sat_points_2d = sat_points_2d.reshape(-1, 2)
                cam_points_2d = cam_points_2d.reshape(-1, 2)

                points_3d_sat = np.array(points_3d_lib[sat_id], dtype=np.float64)
                points_3d_cam = np.array(points_3d_lib[cam_id], dtype=np.float64)
                points_3d_sat = np.tile(points_3d_sat, (n, 1))
                points_3d_cam = np.tile(points_3d_cam, (n, 1))

                camera_K = np.array(camera_K, dtype=np.float64)
                K = camera_K
                retval, sat_rotation_pred, sat_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_sat,
                    sat_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                sat_R, _ = cv2.Rodrigues(sat_rotation_pred)

                retval, cam_rotation_pred, cam_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_cam,
                    cam_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                cam_R, _ = cv2.Rodrigues(cam_rotation_pred)

                results['sate_pose']['sat_R'] = sat_R
                results['sate_pose']['cam_R'] = cam_R

                # 以下是可视化代码，用于验证，实际运行不需要
                sat_translation_pred[0] = 0
                sat_translation_pred[1] = 0
                sat_translation_pred[2] = 800000
                img_pose_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
                save_path = os.path.join(base_save_path, '{}_pose_recog.png'.format(im_id))
                draw_pose_result(img_pose_vis, sat_R, sat_translation_pred, save_path)

get_calibration_data()
