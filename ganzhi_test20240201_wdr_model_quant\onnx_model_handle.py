import os, time
import numpy as np
from PIL import Image
from seg_utils import rawOutput2SegMap, segMapResize, componentFeature, calRealSize, imageResize
from yolov5_utils import filter_box, im_crop, draw_det_box
from seg_vis import draw_points, save_results, draw_seg_box
import onnxruntime
from pathlib import Path
import cv2
import json
from pose_vis import draw_pose_result

# onnx quantation
import onnx
import copy
import collections
import struct
from onnxruntime.quantization import CalibrationDataReader, QuantFormat, quantize_static, QuantType, CalibrationMethod
from onnx import numpy_helper 

#def get_calibration_data():
def model_test(onnx_model_path):
#def get_calibration_data(wdr_onnx_model_path):
    # add by luis
    crop_resize_img_pose_all = []
    sat_iden_all = []

    # 参数设置
    det_sate_input_size = 640  # 整星检测输入尺寸
    det_component_input_size=640  # 部件检测输入尺寸
    seg_input_size = 576  # 部件分割输入尺寸
    reg_identity_input_size=640  # 身份识别输入尺寸
    pose_estimation_size = 512   # 姿态估计输入尺寸

    base_save_path = './log/im_result'
    os.makedirs(base_save_path, exist_ok=True)

    key_points_3d_dir ='./bbox_5mdl_scaled_pad.json'
    camera_K_dir  ='./camera_K.json'

    CLASSES_sate = ['satellite']
    CLASSES_component = ['panel', 'aerial', 'nozzle', 'optical', 'holder', 'sensor', 'docking', 'main', 'arm', 'load',
                         'aerial-pole', 'aerial-unknown', 'others']

    COLOR_sate =[(255,0, 0)]

    COLOR_component = [(255,0,0), (0,255,0),(255,255,0),(0,0,255),(255,0,255),(0,0,124),(202,202,202),(255,255,255),
                       (124,0,0),(0,124,0),(124,124,0),(0,124,124),(0,255,255)]


    # 定义模型
    det_sate_model = onnxruntime.InferenceSession('./onnx_model/yolov5_sate_gray_{}.onnx'.format(det_sate_input_size))
    det_component_model = onnxruntime.InferenceSession('./onnx_model/yolov5_gray_{}.onnx'.format(det_component_input_size))
    #reg_identity_model = onnxruntime.InferenceSession('./onnx_model/sat_recog_model_last.onnx')
    seg_component_model = onnxruntime.InferenceSession('./onnx_model/upernet_mobilenet_{}.onnx'.format(seg_input_size))
    pose_model = onnxruntime.InferenceSession('./onnx_model/end2end0117.onnx')

    reg_identity_model = onnxruntime.InferenceSession(onnx_model_path)

    #pose_model = onnxruntime.InferenceSession('./onnx_model/quant_onnx_model/wdr_quant_v3.onnx')  ### v3版本 量化结果是最好的。# activation_type = QuantType.QInt8, weight_type = QuantType.QInt8, # 参数类型 Int8 / UInt8
    # pose_model = onnxruntime.InferenceSession(wdr_onnx_model_path)

    # 读取测试数据
    data_base_path = './dummy_data'
    data_names = os.listdir(os.path.join(data_base_path, 'src'))

    im_ids = []
    images = []
    labels = []
    for ii, line in enumerate(data_names):
        _image = os.path.join(data_base_path, 'src', line)
        _cat = os.path.join(data_base_path, 'label', line)
        assert os.path.isfile(_image)
        assert os.path.isfile(_cat)

        im_ids.append(str(Path(line).stem))
        images.append(_image)
        labels.append(_cat)

    results = {'sate_coord': [], 'sate_id': {}, 'sate_comp': [], 'sate_pose': {}}

    for image, label, im_id in zip(images, labels, im_ids):
        # st_time = time.time()
        # -----------------------图片读取与预处理-----------------------------
        # 读取图片和分割标签
        _img = Image.open(image).convert('L')  # 单通道
        _target = Image.open(label)  # 分割图

        img_gray = np.array(_img).astype(np.uint8)

        # 图片尺寸变换
        img = cv2.resize(img_gray.astype(np.float32), (det_sate_input_size, det_sate_input_size))
        # 图片像素归一化
        img /= 255.0
        # 图片通道扩展
        img = np.expand_dims(img, axis=0)
        img = np.expand_dims(img, axis=0)
        # ---------------------------------------------------------------

        # -------------------------------整星检测--------------------------
        pred = det_sate_model.run(['output'], {'images': img})[0]
        # 整星边界框NMS过滤与合并
        outbox = filter_box(pred, 0.5, 0.5)
        # ---------------------------------------------------------------

        if outbox.shape[0] > 0:
            # ------------根据检测整星结果对目标所在区域进行图像切片-----------
            rate_w = img_gray.shape[-1] / img.shape[-1]
            rate_h = img_gray.shape[-2] / img.shape[-2]
            crop_coor = im_crop(outbox, [img_gray.shape[-2], img_gray.shape[-1]], rate_h, rate_w)
            img_crop_gray = img_gray[crop_coor[1]:crop_coor[3]+1, crop_coor[0]:crop_coor[2]+1]
            crop_size = [img_crop_gray.shape[-1], img_crop_gray.shape[-2]]
            results['sate_coord'].append(crop_coor)
            # 保存整星切片图像，用于验证，实际不需要
            cv2.imwrite(os.path.join(base_save_path, '{}_crop.png'.format(im_id)), img_crop_gray)
            img_sate_det_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
            img_sate_det_vis = draw_det_box(img_sate_det_vis, outbox, CLASSES_sate, COLOR_sate, rate_h, rate_w)
            cv2.imwrite(os.path.join(base_save_path, '{}_sate_det_vis.png'.format(im_id)), img_sate_det_vis)
            # ---------------------------------------------------------------------

            # -----------------------身份识别-----------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_rec = cv2.resize(img_crop_gray.astype(np.float32), (reg_identity_input_size, reg_identity_input_size))
            crop_resize_img_rec /= 255
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)

            """
            with open('crop_resize_img_rec_19.bin', 'wb')as fp:
                for data in crop_resize_img_rec:
                    for data1 in data:
                        for data2 in data1:
                            for data3 in data2:
                                #a = struct.pack('B', x)
                                a = struct.pack('f', data3)
                                fp.write(a)

            fp.close()
            """
            
            ### *******************************************************change by luis begin
            # get onnx model output
            onnx_outputs = list(map(lambda x:x.name, reg_identity_model.get_outputs()))
            output = reg_identity_model.run(onnx_outputs, {'input': crop_resize_img_rec})
            onnx_outputs_result = [x.name for x in reg_identity_model.get_outputs()]
            ort_outs = collections.OrderedDict(zip(onnx_outputs_result, output))

            # 查看每一层的输出结果
            for i in ort_outs.keys():
                print(i)
                # demo 只举例img这一个输出，其他输出类似  ****注意：需要改变****
                # if (("input" in i)):
                #if (("quantized" in i)):
                #    print(ort_outs[i].shape)
#
                #    # 仅查看结果
                #    # 保存结果到文件中
                #    file_name = i.replace('/', '_')
                #    result_file_path = './quant_mobileNet_output_result_file_20240407/'+ file_name
                #    with open(result_file_path, 'wb') as outfile:
                #        for data1 in ort_outs[i]:
                #            for data2 in data1:
                #                for data3 in data2:
                #                    for data4 in data3:
                #                        a = struct.pack('i', data4)  # 量化后的结果应该为uint8类型
                #                        outfile.write(a)
                #    outfile.close()
                
                # if (("535_quantized" in i) | ("output_cls_quantized" in i)):
                if (("output_cls_quantized" in i)):
                    print(ort_outs[i].shape)
#
                    # 仅查看结果
                    # 保存结果到文件中
                    file_name = i.replace('/', '_')
                    result_file_path = './quant_mobileNet_output_result_file_20240407/'+ file_name
                    with open(result_file_path, 'wb') as outfile:
                        for data1 in ort_outs[i]:
                            for data2 in data1:
                                a = struct.pack('i', data2)  # 量化后的结果应该为uint8类型
                                outfile.write(a)
                    outfile.close()
            ### *******************************************************change by luis end
            

            # 输入分类网络模型
            output_class_test = reg_identity_model.run(['output_cls'], {'input': crop_resize_img_rec})
            output_class = reg_identity_model.run(['output_cls'], {'input': crop_resize_img_rec})[0]

            # 从网络输出的类别得分中取出实际存在的类型得分（前者大于后者，因为考虑后续任务对类别数量的拓展性，这里实际存在类别数量暂定为19）
            output_class_real_c = output_class[:, 0:19]
            # softmax
            output_class_real_c_exp = np.exp(output_class_real_c)
            output_class_real_c_exp = output_class_real_c_exp / np.sum(output_class_real_c_exp, axis=1, keepdims=True)
            # 在从有效类别得分中取出任务场景相关的类别，可能会根据场景设定不同类别子集，这里以取前5类为例
            output_class_temp = output_class_real_c_exp[:, 0:5]
            # 获取身份id下标和置信度得分
            pred_class = np.argmax(output_class_temp, axis=1)
            pred_class_p = np.max(output_class_temp, axis=1)
            results['sate_id']['id'] = pred_class
            results['sate_id']['conf'] = pred_class_p
            # ------------------------------------------------------------------------------

            # -----------------------部件检测----------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_com = cv2.resize(img_crop_gray.astype(np.float32), (det_component_input_size, det_component_input_size))
            crop_resize_img_com /= 255
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            # 图像输入网络
            pred_com = det_component_model.run(['output'], {'images': crop_resize_img_com})[0]
            # NMS
            outbox_com = filter_box(pred_com, 0.5, 0.5)
            if outbox_com.shape[0] > 0:
                results['sate_comp'].append(outbox_com)

                # 可视化，用于效果验证，实际运行不需要
                rate_w = img_crop_gray.shape[-1] / crop_resize_img_com.shape[-1]
                rate_h = img_crop_gray.shape[-2] / crop_resize_img_com.shape[-2]
                img_com_det_vis = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
                img_com_det_vis = draw_det_box(img_com_det_vis, outbox_com, CLASSES_component, COLOR_component, rate_h, rate_w)
                cv2.imwrite(os.path.join(base_save_path, '{}_com_det_vis.png'.format(im_id)), img_com_det_vis)
            else:
                print('No component detected.')
            # --------------------------------------------------------------------------

            # --------------------部件分割--------------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_seg = cv2.resize(img_crop_gray.astype(np.float32), (seg_input_size, seg_input_size))
            crop_resize_img_seg /= 255
            crop_resize_img_seg = np.expand_dims(crop_resize_img_seg, axis=0)
            crop_resize_img_seg = np.expand_dims(crop_resize_img_seg, axis=0)
            seg = seg_component_model.run(['output_seg'], {'input': crop_resize_img_seg})[0]
            seg = seg[0]
            # 模型输出转分割图
            segmap = rawOutput2SegMap(seg)
            #分割图变换至原始尺寸
            output_segmap_orisize = segMapResize(segmap, crop_size)

            out_segmap = np.zeros_like(output_segmap_orisize)

            # 提取部件特征，包括误检测滤除，联通域提取，最小外接矩形提取
            out_segmap1, out_points1 = componentFeature(output_segmap_orisize.copy(), 'fanban')
            if out_segmap1 is not None:
                out_segmap += out_segmap1

            out_segmap2, out_points2 = componentFeature(output_segmap_orisize.copy(), 'tianxian')
            if out_segmap2 is not None:
                out_segmap += out_segmap2

            out_segmap3, out_points3 = componentFeature(output_segmap_orisize.copy(), 'xiangji')
            if out_segmap3 is not None:
                out_segmap += out_segmap3

            out_segmap4, out_points4 = componentFeature(output_segmap_orisize.copy(), 'benti')
            if out_segmap4 is not None:
                out_segmap += out_segmap4

            # 部件尺寸计算
            if out_points1 is not None:
                real_size1 = calRealSize(out_points1[:, 5:, :], 0.07)
            else:
                real_size1 = None

            if out_points2 is not None:
                real_size2 = calRealSize(out_points2[:, 5:, :], 0.07)
            else:
                real_size2 = None

            if out_points3 is not None:
                real_size3 = calRealSize(out_points3[:, 5:, :], 0.07)
            else:
                real_size3 = None

            if out_points4 is not None:
                real_size4 = calRealSize(out_points4[:, 5:, :], 0.07)
            else:
                real_size4 = None

            out_points = {1: [out_points1, real_size1], 2: [out_points2, real_size2], 3: [out_points3, real_size3],
                          4: [out_points4, real_size4]}
            # ed_time = time.time()

            # 以下都是可视化和数据保存的内容，实际运行不需要
            img_seg_vis_ori = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
            img_seg_vis = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
            image_dp = draw_points(img_seg_vis, out_points)
            image_dp = image_dp[:, :, ::-1]
            save_results(im_id, img_seg_vis_ori, out_segmap, image_dp, base_save_path)
            # ----------------------------------------------------------------------------
            # -----------------------相对姿态估计--------------------------------------------
            # 读取模型以及相机三维点以及相机内参，用于相对姿态解算
            with open(key_points_3d_dir, 'r') as f:
                points_3d_lib = json.load(f)
            with open(camera_K_dir, 'r') as f:
                camera_K = json.load(f)
            # 输入图像尺寸变换
            crop_resize_img_pose = cv2.resize(img_crop_gray.astype(np.float32), (pose_estimation_size, pose_estimation_size))
            crop_resize_img_pose /= 255
            crop_resize_img_pose = np.stack([crop_resize_img_pose, crop_resize_img_pose, crop_resize_img_pose], axis=0)
            crop_resize_img_pose = np.expand_dims(crop_resize_img_pose, axis=0)
            # 根据前面识别的目标身份id，变换至目标三维点读取下标
            pred_class_pose = pred_class[0]
            pred_class_id = pred_class_pose + 1
            sat_id = 2 * pred_class_id - 2
            cam_id = 2 * pred_class_id - 1
            sat_iden = np.array([sat_id], dtype=np.int64)
            
            crop_resize_img_pose_all.append(crop_resize_img_pose)
            sat_iden_all.append(sat_iden)
            
            
            # 图像输入姿态估计网络，得到预测的模型和相机关键点二维坐标
            ### ori code 
            #pose_result = pose_model.run(['sat_keypoints_2d', 'cam_keypoints_2d'], {'img': crop_resize_img_pose, 'img_metas': sat_iden})
            #sat_points_2d = pose_result[0]
            #cam_points_2d = pose_result[1]
            """
            ### *******************************************************change by luis begin
            onnx_outputs = list(map(lambda x:x.name, pose_model.get_outputs()))
            output = pose_model.run(onnx_outputs, {'img': crop_resize_img_pose, 'img_metas': sat_iden})
            onnx_outputs_result = [x.name for x in pose_model.get_outputs()]
            ort_outs = collections.OrderedDict(zip(onnx_outputs_result, output))

            # 查看每一层的输出结果
            for i in ort_outs.keys():
                print(i)
                # demo 只举例img这一个输出，其他输出类似  ****注意：需要改变****
                if (("img" in i)):
                    print(ort_outs[i].shape)

                    # 仅查看结果
                    # 保存结果到文件中
                    file_name = i.replace('/', '_')
                    result_file_path = './output_result_file/'+ file_name
                    with open(result_file_path, 'wb') as outfile:
                        for data1 in ort_outs[i]:
                            for data2 in data1:
                                for data3 in data2:
                                    for data4 in data3:
                                    #a = struct.pack('i', data2)  # 量化后的结果应该为uint8类型
                                        a = struct.pack('i', data4)  # 量化后的结果应该为uint8类型
                                outfile.write(a)
                    outfile.close()
            ### *******************************************************change by luis end
            """

            # 为了使后面的代码顺利运行，继续执行pose model，以只得到sat_keypoints_2d 和cam_keypoints_2d这两个输出结果
            pose_result = pose_model.run(['sat_keypoints_2d', 'cam_keypoints_2d'], {'img': crop_resize_img_pose, 'img_metas': sat_iden})
            sat_points_2d = pose_result[0]
            cam_points_2d = pose_result[1]

            if sat_points_2d.shape[0] > 0:
                # pnp 解算姿态
                n = sat_points_2d.shape[0]
                sat_points_2d = sat_points_2d.reshape(-1, 2)
                cam_points_2d = cam_points_2d.reshape(-1, 2)

                points_3d_sat = np.array(points_3d_lib[sat_id], dtype=np.float64)
                points_3d_cam = np.array(points_3d_lib[cam_id], dtype=np.float64)
                points_3d_sat = np.tile(points_3d_sat, (n, 1))
                points_3d_cam = np.tile(points_3d_cam, (n, 1))

                camera_K = np.array(camera_K, dtype=np.float64)
                K = camera_K
                retval, sat_rotation_pred, sat_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_sat,
                    sat_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                sat_R, _ = cv2.Rodrigues(sat_rotation_pred)

                retval, cam_rotation_pred, cam_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_cam,
                    cam_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                cam_R, _ = cv2.Rodrigues(cam_rotation_pred)

                results['sate_pose']['sat_R'] = sat_R
                results['sate_pose']['cam_R'] = cam_R

                
                # 以下是可视化代码，用于验证，实际运行不需要
                sat_translation_pred[0] = 0
                sat_translation_pred[1] = 0
                sat_translation_pred[2] = 800000
                img_pose_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
                save_path = os.path.join(base_save_path, '{}_pose_recog.png'.format(im_id))
                draw_pose_result(img_pose_vis, sat_R, sat_translation_pred, save_path)
                
            else:
                print('Unable to recognize target pose.')
            
            # -------------------------------------------------------------------------
            print('results: {}'.format(results))
        else:
            print('No target sate detect.')
    return crop_resize_img_pose_all, sat_iden_all


##########################################################################################
# WDR Onnx Model quant
class DataReader(CalibrationDataReader):
    def __init__(self):
        self.enum_data = None
        self.enum_data1 = None
        self.enum_data2 = None
        # get input data
        self.crop_resize_img_pose, self.sat_iden = get_calibration_data()
        #self.x_input_all, self.d_input_all = get_calibration_data()
        #self.module_data  = get_calibration_data()
        #self.input_name = session.get_inputs()[0].name
        #self.datasize_1 = len(self.x_input_all)
        self.datasize_1 = len(self.crop_resize_img_pose)
        self.datasize_2 = 0
        self.list_num = 0

        self.data_num = 0
        self.data_num1 = 0
        self.data_valid_flag = 0
        self.data_final = None

    def get_next(self):
        if self.enum_data is None:
            self.calibration_data = []
            for i in range(self.datasize_1):
                data1 = {'img': self.crop_resize_img_pose[i], 'img_metas': self.sat_iden[i]}
                self.calibration_data.append(data1)
            self.enum_data = iter(self.calibration_data)

        return next(self.enum_data, None)      
    #def get_next(self):
    #    if self.enum_data is None:
    #        self.calibration_data = []
    #        for i in range(self.datasize_1):
    #            data1 = {'x_input': self.x_input_all[i], 'd_input': self.d_input_all[i]}
    #            self.calibration_data.append(data1)
    #        self.enum_data = iter(self.calibration_data)
#
    #    return next(self.enum_data, None)        
    
def wdr_onnx_model_quantation():
    #----------------------------------------------------------------------
    ori_onnx_path = './onnx_model/end2end0117.onnx'
    out_onnx_path = './onnx_model/quant_onnx_model/wdr_quant_v1.onnx'
    #shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'
    #onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

    data_reader = DataReader()
    quantize_static(
        model_input = ori_onnx_path, # 输入模型
        model_output = out_onnx_path, # 输出模型
        calibration_data_reader = data_reader, # 校准数据读取器
        quant_format = QuantFormat.QOperator, # 量化格式 QDQ / QOperator
        #quant_format = QuantFormat.QDQ, # 量化格式 QDQ / QOperator
        activation_type = QuantType.QUInt8, # 激活类型 Int8 / UInt8
        weight_type = QuantType.QUInt8, # 参数类型 Int8 / UInt8
        #weight_type = QuantType.QInt8, # 参数类型 Int8 / UInt8
        calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
        #calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
        #per_channel=True,
        #extra_options = extra_options,
        #optimize_model=False # 是否优化模型
    )
    
    print("quant finish")

# -------------------------------------------------------------------------------------------------------
# add onnx output model
def add_onnx_output(model_name, model_onnx_path):
    model_onnx = onnx.load(model_onnx_path)
    
    # 更改网络模型，添加输出节点
    ori_output = copy.deepcopy(model_onnx.graph.output)

    for node in model_onnx.graph.node:
        if ('QuantizeLinear' in node.name):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])

        if ('Conv' in node.name):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])
        
        if (('Concat' in node.name) | ('MaxPool' in node.name) | ('add' in node.name) | ('Mul' in node.name) | 
            ('Add' in node.name) | ('GlobalAveragePool' in node.name) | ('Gemm' in node.name) | ('DequantizeLinear' in node.name) | ('Flatten' in node.name)):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])

        if ('Conv_179' in node.name):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])

        if ('Conv_160' in node.name):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])
        
        if (('Conv_141' in node.name)):
            print(node.name)
            for output in node.output:
                model_onnx.graph.output.extend([onnx.ValueInfoProto(name=output)])
    # Save Model
    # 保存一次即可
    f = model_onnx.SerializeToString()
    file = open(model_name, "wb")
    file.write(f)
    file.close()

def get_quant_model_weight_bin(onnx_path):
    onnx_model = onnx.load(onnx_path) #加载onnx模型
    for i in range(len(onnx_model.graph.node)):
        if onnx_model.graph.node[i].name == "input":
            Node = onnx_model.graph.node[i]
            print(Node)
    
    for initializer in onnx_model.graph.initializer:
        if (('model.classifier' in initializer.name) & ('weight_quantized' in initializer.name)):
            tensor_name = initializer.name
            tensor_name = tensor_name.replace('/', '_')
            if ('::' in tensor_name):
                tensor_name = tensor_name.replace('::', '_')
            tensor_value = numpy_helper.to_array(initializer)
            
            print(tensor_name)
            print(tensor_value.shape)

            # 保存tensor value到txt文档中
            quant_weight_file_path = './quant_mobileNet_weight_file_20240407/'+ tensor_name + '_trans'
            
            tensor_value_trans = np.transpose(tensor_value)
            print(tensor_value_trans.shape)
            with open(quant_weight_file_path, 'wb') as outfile:
                if (tensor_value_trans.ndim == 1):
                    for data in tensor_value:
                            a = struct.pack('i', data)          #integer
                            outfile.write(a)     
                elif (tensor_value_trans.ndim == 2):
                    for data1 in tensor_value_trans:
                            for data2 in data1:
                                a = struct.pack('i', data2)          #integer
                                outfile.write(a)   
            outfile.close() 
            

        # if ('scale' in initializer.name):
        #     tensor_name = initializer.name
        #     tensor_name = tensor_name.replace('/', '_')
        #     if ('::' in tensor_name):
        #         tensor_name = tensor_name.replace('::', '_')
        #     tensor_value = numpy_helper.to_array(initializer)
            
        #     print(tensor_name)
        #     print(tensor_value.shape)

        #     # 保存tensor value到txt文档中
        #     quant_weight_file_path = './quant_mobileNet_weight_file_20240407/'+ tensor_name
            
        #     with open(quant_weight_file_path, 'wb') as outfile:
        #         a = struct.pack('f', tensor_value)
        #         outfile.write(a)
        #     outfile.close() 

        # if ('zero' in initializer.name):
        #     tensor_name = initializer.name
        #     tensor_name = tensor_name.replace('/', '_')
        #     if ('::' in tensor_name):
        #         tensor_name = tensor_name.replace('::', '_')
        #     tensor_value = numpy_helper.to_array(initializer)
            
        #     print(tensor_name)
        #     print(tensor_value.shape)

        #     # 保存tensor value到txt文档中
        #     quant_weight_file_path = './quant_mobileNet_weight_file_20240407/'+ tensor_name
            
        #     with open(quant_weight_file_path, 'wb') as outfile:
        #         a = struct.pack('i', tensor_value)
        #         outfile.write(a)
        #     outfile.close()
        #     print("finish")
        
        # if (('quantized' in initializer.name)):
        #     tensor_name = initializer.name
        #     tensor_name = tensor_name.replace('/', '_')
        #     if ('::' in tensor_name):
        #         tensor_name = tensor_name.replace('::', '_')
        #     tensor_value = numpy_helper.to_array(initializer)
            
        #     print(tensor_name)
        #     print(tensor_value.shape)

        #     # 保存tensor value到txt文档中
        #     Weight_file_path = './quant_mobileNet_weight_file_20240407/'+ tensor_name

        #     with open(Weight_file_path, 'wb') as outfile:
        #         if (tensor_value.ndim == 1):
        #             for data in tensor_value:
        #                     a = struct.pack('i', data)          #integer
        #                     outfile.write(a)     
        #         elif (tensor_value.ndim == 2):
        #             for data1 in tensor_value:
        #                     for data2 in data1:
        #                         a = struct.pack('i', data2)          #integer
        #                         outfile.write(a)   
        #         elif (tensor_value.ndim == 3):
        #             for data1 in tensor_value:
        #                 for data2 in data1:
        #                     for data3 in data2:
        #                         a = struct.pack('i', data3) #integer
        #                         outfile.write(a)  
        #         elif (tensor_value.ndim == 4):
        #             for data1 in tensor_value:
        #                 for data2 in data1:
        #                     for data3 in data2:
        #                         for data4 in data3:
        #                             a = struct.pack('i', data4) #integer
        #                             outfile.write(a)  
        #     outfile.close()

def del_specific_optype_node(ori_model_path, out_model_save_path):
    model = onnx.load(ori_model_path)

    for node in model.graph.node:
        # if ((node.op_type == "Identity") | (node.op_type == "Transpose") | (node.op_type == "Sigmoid") |
        #     (node.op_type == "Split")    | (node.op_type == "Mul") | (node.op_type == "Pow")):
        #     print(node.name)
        #     model.graph.node.remove(node)
        # if ((node.name == "Add_149") | (node.name == "Concat_196") | (node.name == "Reshape_197") |
        #     (node.name == "Add_168") | (node.name == "Concat_177") | (node.name == "Reshape_178") |
        #     (node.name == "Add_187") | (node.name == "Concat_198") | (node.name == "Concat_158")  | (node.name == "Reshape_159")):
        #     print(node.name)
        #     model.graph.node.remove(node)
        if ((node.name == "Reshape_161") | (node.name == "Reshape_180") | (node.name == "Reshape_142")):
            print(node.name)
            model.graph.node.remove(node)

    ## 保存更新后的模型为新的ONNX文件
    onnx.save(model, out_model_save_path)

from onnx import shape_inference
def del_output_node(ori_model_path, out_model_save_path):
    # step1 : 删除不用的输入输出节点
    # 加载已有的ONNX模型
    model = onnx.load(ori_model_path)

    output_name = "output"  # 假设要删除名为 "output" 的输出节点
    for i, node in enumerate(model.graph.node):
        if output_name in node.output:
            del model.graph.node[i]
            break

    # 更新模型输出信息
    output_names = [output.name for output in model.graph.output]
    if output_name in output_names:
        model.graph.output.remove(model.graph.output[output_names.index(output_name)])

    # 重新推断模型形状
    inferred_model = shape_inference.infer_shapes(model)

    # 保存更新后的模型为新的 ONNX 文件
    onnx.save(inferred_model, out_model_save_path)

def del_input_node(ori_model_path, out_model_save_path):
    model = onnx.load(ori_model_path)
    # 找到要删除的输入节点
    input_node_name = "images"
    input_node = None
    for node in model.graph.input:
        if node.name == input_node_name:
            input_node = node
            break

    # 删除输入节点
    model.graph.input.remove(input_node)

    # 更新模型的输入列表
    #model.graph.input[:] = [input for input in model.graph.input if input.name != input_node_name]

    # 保存更新后的模型为新的ONNX文件
    onnx.save(model, out_model_save_path)

import onnxruntime as ort
import onnx.helper as helper

def add_input_node(ori_model_path, out_model_save_path):
    model = onnx.load(ori_model_path)

    new_input = helper.make_tensor_value_info('images', onnx.TensorProto.FLOAT, [1,1,640,640])  # 示例：新输入名称为 x_input，数据类型为 FLOAT，形状为 [1, 3, 224, 224]
    
    # 找到Concat操作节点
    concat_node = None
    for node in model.graph.node:
        #if node.op_type == "Concat": # fp32 model
        if node.op_type == "Conv" and node.name == "Conv_0": # quant model
            concat_node = node
            break
    
    # 将新的输入节点连接到Concat操作的第二个输入
    concat_node.input.append("images")
    model.graph.input.extend([new_input])

    onnx.save(model, out_model_save_path)

if __name__ == '__main__':
    # step1 model quant
    #wdr_onnx_model_quantation()

    # step2 给onnx model添加输出节点
    # add_onnx_output('./onnx_model/quant_onnx_model/mobilenet_quant_v3_output_v2.onnx', './onnx_model/quant_onnx_model/mobilenet_quant_v3.onnx')
    # add_onnx_output('./onnx_model/handle_onnx_model/yolov5_gray_640_new_v2.onnx', './onnx_model/handle_onnx_model/yolov5_gray_640_new_v1.onnx')
    add_onnx_output('./sqy_onnx_model/mobilenet_quant_v3_output_v2_output.onnx', './sqy_onnx_model/mobilenet_quant_v3_output_v2.onnx')

    # step3 model test
    #get_calibration_data('./onnx_model/quant_onnx_model/wdr_quant_v3.onnx')  ## quant version
    #get_calibration_data('./onnx_model/quant_onnx_model/wdr_quant_v3_output_v1.onnx') ## quant version + output node

    #model_test('./onnx_model/quant_onnx_model/mobilenet_quant_v3.onnx') ## quant version + output node

    # step4 get onnx model weight parameter
    # get_quant_model_weight_bin('./onnx_model/quant_onnx_model/mobilenet_quant_v3_output_v1.onnx')

    # step5 get onnx model output result
    # model_test('./onnx_model/quant_onnx_model/mobilenet_quant_v3_output_v2.onnx')

    # step6: onnx model 裁剪
    # del_specific_optype_node('./onnx_model/yolov5_gray_640.onnx', './onnx_model/handle_onnx_model/yolov5_gray_640_new_v1.onnx')
    # del_specific_optype_node('./onnx_model/handle_onnx_model/yolov5_gray_640_new_v1.onnx', './onnx_model/handle_onnx_model/yolov5_gray_640_new_v1.onnx')

    # del_output_node('./onnx_model/handle_onnx_model/yolov5_gray_640_new_v2.onnx', './onnx_model/handle_onnx_model/yolov5_gray_640_new_v3.onnx')
    # add_input_node('./onnx_model/handle_onnx_model/yolov5_gray_640_new_v4.onnx', './onnx_model/handle_onnx_model/yolov5_gray_640_new_v5.onnx')

    # get_quant_model_weight_bin('./onnx_model/encoder_quant_v3.onnx')