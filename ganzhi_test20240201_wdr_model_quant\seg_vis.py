import os
import numpy as np
import cv2
from PIL import Image

def get_satellite_labels_6inst():
    """Load the mapping that associates satellite classes with label colors
    Returns:
        np.ndarray with dimensions (3, 3)
    """
    return np.asarray([[0, 0, 0], [255, 0, 0], [0, 255, 0], [0, 0, 255], [255, 255, 255],
                       [0, 124, 0], [124, 0, 0]])    #return np.asarray([[0, 0, 0], [255, 0, 0], [0, 255, 0]])

color_map = get_satellite_labels_6inst()

def label2colormap(label):
    types = np.int32(np.unique(label))
    r, g, b = np.zeros_like(label), np.zeros_like(label), np.zeros_like(label)

    for i in range(types.shape[0]):
        r[label == types[i]] = color_map[types[i]][0]
        g[label == types[i]] = color_map[types[i]][1]
        b[label == types[i]] = color_map[types[i]][2]

    rgb = np.dstack((r, g, b))
    return rgb.astype('uint8')

def draw_points(image, points):
    id2text = {1: 'fanban', 2: 'tianxian', 3: 'xiangji', 4: 'benti', 5: 'zaiwei', 6: 'jixiebi'}
    for id, data in points.items():
        comps = data[0]
        real_s = data[1]
        if comps is not None:
            color = color_map[id]
            color_cv = tuple([int(color[2]), int(color[1]), int(color[0])])
            for i in range(comps.shape[0]):
                c = comps[i]
                center = c[0]
                rect = c[1:5]
                cv2.drawContours(image, [rect], 0, color_cv, 4)
                cv2.drawMarker(image, (center[0], center[1]), color_cv, markerType=0, thickness=4)

                real_size = real_s[i][0]
                # cv2.putText(img=image, text='{}:{:.2f}*{:.2f}'.format(id2text[id], real_size[0], real_size[1]),
                #             org=(center[0], center[1]),
                #             fontFace=cv2.FONT_HERSHEY_SIMPLEX, fontScale=1, color=color_cv, thickness=2)
    return image

def save_results(sample_id, image, pred_seg, image_dp, base_save_path):
    image_ori = image
    pred_seg = pred_seg

    image_ori = image_ori.astype('uint8')

    image_dp_PIL = Image.fromarray(image_dp)
    image_dp_PIL = image_dp_PIL.convert('RGBA')

    image_ori_PIL = Image.fromarray(image_ori)
    image_ori_PIL = image_ori_PIL.convert('RGBA')
    w, h = image_ori_PIL.size

    pred_cm = label2colormap(pred_seg)

    pred_cm = Image.fromarray(pred_cm)
    pred_cm = pred_cm.convert('RGBA')

    pred_blend = Image.blend(image_ori_PIL, pred_cm, 0.4)

    results = Image.new('RGBA', (w * 3, h))
    results.paste(image_ori_PIL, (0, 0))
    results.paste(pred_blend, (w * 1, 0))
    results.paste(image_dp_PIL, (w * 2, 0))

    results.save(os.path.join(base_save_path, '{}_seg_vis.png'.format(sample_id)))

def draw_seg_box(image, box_data):
    image_d = image.copy()
    cv2.rectangle(image_d, (box_data[0], box_data[1]), (box_data[2], box_data[3]), (255, 0, 0), 2)
    cv2.putText(img=image_d, text='crop size:{:.2f}*{:.2f}'.format(box_data[2]-box_data[0], box_data[3]-box_data[1]),
                org=(box_data[0]+10, box_data[1]),
                fontFace=cv2.FONT_HERSHEY_SIMPLEX, fontScale=1, color=(255, 0, 0), thickness=2)
    return image_d