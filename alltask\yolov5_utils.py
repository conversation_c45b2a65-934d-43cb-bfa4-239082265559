# import torch
import cv2
import numpy as np
import onnxruntime


class YOLOV5():
    def __init__(self, onnxpath):
        self.onnx_session = onnxruntime.InferenceSession(onnxpath,providers=['TensorrtExecutionProvider', 'CUDAExecutionProvider', 'CPUExecutionProvider'])
        self.input_name = self.get_input_name()
        self.output_name = self.get_output_name()

    # -------------------------------------------------------
    #   get input name and output name
    # -------------------------------------------------------
    def get_input_name(self):
        input_name = []
        for node in self.onnx_session.get_inputs():
            input_name.append(node.name)
        return input_name

    def get_output_name(self):
        output_name = []
        for node in self.onnx_session.get_outputs():
            output_name.append(node.name)
        return output_name

    # -------------------------------------------------------
    #   input image
    # -------------------------------------------------------
    def get_input_feed(self, img_tensor):
        input_feed = {}
        for name in self.input_name:
            input_feed[name] = img_tensor
        return input_feed

    # -------------------------------------------------------
    #	1.BGR2RGB HWC2CHW
    #	2.normalization
    #	3.expand dimension
    #	4.onnx_session inference
    # -------------------------------------------------------
    def inference(self, img):
        img = np.expand_dims(img, axis=0)
        input_feed = self.get_input_feed(img)
        pred = self.onnx_session.run(None, input_feed)[0]
        return pred


def preprocessing(img, size):
    or_img = cv2.resize(img, size)
    img = or_img[:, :, ::-1].transpose(2, 0, 1)  # BGR2RGB HWC2CHW
    img = img.astype(dtype=np.float32)  # FP16: float16   int8: float32
    img = img / 255.0  # normalization
    return img


# dets:  array [x1,y1,x2,y2,score,class]
def nms(dets, thresh):
    x1 = dets[:, 0]
    y1 = dets[:, 1]
    x2 = dets[:, 2]
    y2 = dets[:, 3]
    # -------------------------------------------------------
    #   compute area of the box
    #	sort scores from largest to smallest
    # -------------------------------------------------------
    areas = (y2 - y1 + 1) * (x2 - x1 + 1)
    scores = dets[:, 4]
    keep = []
    index = scores.argsort()[::-1]

    while index.size > 0:
        i = index[0]
        keep.append(i)
        # -------------------------------------------------------
        #   compute intersection area
        # -------------------------------------------------------
        x11 = np.maximum(x1[i], x1[index[1:]])  # 逐一对比两array中元素大小并输出较大值
        y11 = np.maximum(y1[i], y1[index[1:]])
        x22 = np.minimum(x2[i], x2[index[1:]])
        y22 = np.minimum(y2[i], y2[index[1:]])

        w = np.maximum(0, x22 - x11 + 1)
        h = np.maximum(0, y22 - y11 + 1)

        overlaps = w * h  # 当前框与其他框的交集面积
        # -------------------------------------------------------
        #   计算该框与其它框的IOU，去除掉重复的框，即IOU值大的框
        #	IOU小于thresh的框保留下来
        # -------------------------------------------------------
        union = areas[i] - overlaps + areas[index[1:]]
        union[np.isinf(union)] = 65000
        ious = overlaps / union
        idx = np.where(ious <= thresh)[0]
        index = index[idx + 1]
    return keep


def xywh2xyxy(x):
    # [x, y, w, h] to [x1, y1, x2, y2]
    y = np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2
    y[:, 1] = x[:, 1] - x[:, 3] / 2
    y[:, 2] = x[:, 0] + x[:, 2] / 2
    y[:, 3] = x[:, 1] + x[:, 3] / 2
    return y


def filter_box(org_box, conf_thres, iou_thres):
    # -------------------------------------------------------
    #   删除为1的维度
    #	删除置信度小于conf_thres的BOX
    # -------------------------------------------------------
    org_box = np.squeeze(org_box)
    conf = org_box[..., 4] > conf_thres
    box = org_box[conf == True]
    # -------------------------------------------------------
    #	通过argmax获取置信度最大的类别
    # -------------------------------------------------------
    cls_cinf = box[..., 5:]
    cls = []
    for i in range(len(cls_cinf)):
        cls.append(int(np.argmax(cls_cinf[i])))
    all_cls = list(set(cls))
    # -------------------------------------------------------
    #   分别对每个类别进行过滤
    #	1.将第6列元素替换为类别下标
    #	2.xywh2xyxy 坐标转换
    #	3.经过非极大抑制后输出的BOX下标
    #	4.利用下标取出非极大抑制后的BOX
    # -------------------------------------------------------
    output = []

    for i in range(len(all_cls)):
        curr_cls = all_cls[i]
        curr_cls_box = []
        curr_out_box = []
        for j in range(len(cls)):
            if cls[j] == curr_cls:
                box[j][5] = curr_cls
                curr_cls_box.append(box[j][:6])
        curr_cls_box = np.array(curr_cls_box)
        # curr_cls_box_old = np.copy(curr_cls_box)
        curr_cls_box = xywh2xyxy(curr_cls_box)
        curr_out_box = nms(curr_cls_box, iou_thres)
        for k in curr_out_box:
            output.append(curr_cls_box[k])
    output = np.array(output)
    return output


def clip_coords(boxes, shape):
    # Clip bounding xyxy bounding boxes to image shape (height, width)
    if isinstance(boxes, torch.Tensor):  # faster individually
        boxes[:, 0].clamp_(0, shape[1])  # x1
        boxes[:, 1].clamp_(0, shape[0])  # y1
        boxes[:, 2].clamp_(0, shape[1])  # x2
        boxes[:, 3].clamp_(0, shape[0])  # y2
    else:  # np.array (faster grouped)
        boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2


def scale_coords(img1_shape, coords, img0_shape):
    # Rescale coords (xyxy) from img1_shape to img0_shape
    gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
    pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding

    coords[:, [0, 2]] -= pad[0]  # x padding
    coords[:, [1, 3]] -= pad[1]  # y padding
    coords[:, :4] /= gain
    clip_coords(coords, img0_shape)
    return coords

def im_crop(outbox, shape_max, rate_h, rate_w):
    outbox[:, :4] = outbox[:, :4].round()
    # get coords of box
    left, top, right, bottom = outbox[..., :4].astype(np.int32)[0]
    top = round(top*rate_h)
    left = round(left*rate_w)
    bottom = round(bottom*rate_h)
    right = round(right*rate_w)
    # compute crop size of input image
    # w_box = bottom - top
    # h_box = right - left
    w_box = right - left
    h_box = bottom - top
    crop_size = min(round(max(w_box, h_box) * 1.5), min(shape_max)) # properly expand the crop area
    # compute coords of crop region
    x1 = max(round((left + right) / 2 - crop_size / 2), 0)
    y1 = max(round((top + bottom) / 2 - crop_size / 2), 0)
    x2 = min(x1 + crop_size, shape_max[1])
    y2 = min(y1 + crop_size, shape_max[0])
    return [x1, y1, x2, y2]

def draw_det_box(image, box_data, CLASSES, COLOR, rate_h, rate_w):

    boxes = box_data[..., :4].astype(np.int32)
    scores = box_data[..., 4]
    classes = box_data[..., 5].astype(np.int32)

    lw = max(round((image.shape[0]+image.shape[1]) / 2 * 0.003), 2)
    tf = max(lw - 1, 1)  # font thickness

    for box, score, cl in zip(boxes, scores, classes):
        left, top, right, bottom = box
        top = round(top * rate_h)
        left = round(left * rate_w)
        bottom = round(bottom * rate_h)
        right = round(right * rate_w)

        #print('class: {}, score: {}'.format(CLASSES[cl], score))
        # print('box coordinate left,top,right,down: [{}, {}, {}, {}]'.format(top, left, right, bottom))
        cv2.rectangle(image, (left, top), (right, bottom), COLOR[cl][::-1], thickness=2, lineType=cv2.LINE_AA)
        # cv2.putText(img, '{0} {1:.2f}'.format(CLASSES[cl], score), (left, top), 0, lw / 3, COLOR[cl][::-1],
        #             thickness=tf, lineType=cv2.LINE_AA)
    return image