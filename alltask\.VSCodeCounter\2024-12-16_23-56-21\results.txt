Date : 2024-12-16 23:56:21
Directory : d:\PROJECT\CASC502\4-Float_Code\alltask
Total : 20 files,  5583 codes, 336 comments, 385 blanks, all 6304 lines

Languages
+----------+------------+------------+------------+------------+------------+
| language | files      | code       | comment    | blank      | total      |
+----------+------------+------------+------------+------------+------------+
| JSON     |          9 |      3,965 |          0 |          0 |      3,965 |
| Python   |         11 |      1,618 |        336 |        385 |      2,339 |
+----------+------------+------------+------------+------------+------------+

Directories
+-----------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                      | files      | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                         |         20 |      5,583 |        336 |        385 |      6,304 |
| . (Files)                                                                                                 |         13 |      3,933 |        319 |        309 |      4,561 |
| Eutelsat-117_data                                                                                         |          6 |      1,188 |          0 |          0 |      1,188 |
| Eutelsat-117_data\json                                                                                    |          6 |      1,188 |          0 |          0 |      1,188 |
| tools                                                                                                     |          1 |        462 |         17 |         76 |        555 |
+-----------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+-----------------------------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+
| filename                                                                                                  | language | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000000.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000001.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000002.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000003.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000004.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000005.json | JSON     |        198 |          0 |          0 |        198 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\bbox_3mdl1219_scaled.json                                         | JSON     |        254 |          0 |          0 |        254 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\bbox_5mdl_scaled_pad.json                                         | JSON     |      2,522 |          0 |          0 |      2,522 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\camera_K.json                                                     | JSON     |          1 |          0 |          0 |          1 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\inference_pth_est_20240417.py                                     | Python   |         65 |          8 |         23 |         96 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_240418.py                                          | Python   |        174 |         40 |         61 |        275 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_alltask_float.py                                   | Python   |        153 |         37 |         42 |        232 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_alltask_quant.py                                   | Python   |        149 |         39 |         45 |        233 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_getoutput.py                                       | Python   |        134 |         13 |         24 |        171 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\pose.py                                                           | Python   |         75 |          4 |         17 |         96 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\pose_vis.py                                                       | Python   |        147 |          5 |         22 |        174 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\seg_utils.py                                                      | Python   |         60 |        118 |         29 |        207 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\seg_vis.py                                                        | Python   |         62 |          3 |         17 |         82 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\tools\visualize.py                                                | Python   |        462 |         17 |         76 |        555 |
| d:\PROJECT\CASC502\4-Float_Code\alltask\yolov5_utils.py                                                   | Python   |        137 |         52 |         29 |        218 |
| Total                                                                                                     |          |      5,583 |        336 |        385 |      6,304 |
+-----------------------------------------------------------------------------------------------------------+----------+------------+------------+------------+------------+