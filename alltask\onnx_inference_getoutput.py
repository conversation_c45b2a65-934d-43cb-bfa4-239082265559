import cv2
import numpy as np
import json
import onnxruntime
import os 
import glob
from PIL import Image
import random
import copy
from collections import OrderedDict
import collections
from scipy.spatial.transform import Rotation
import struct

def convert_sat_matrix(rotation_matrix_sat):
    A = rotation_matrix_sat
    rotation_matrix_sat = np.array([[ A[0, 0], -A[0, 1], -A[0, 2]],
                                    [ A[2, 0], -A[2, 1], -A[2, 2]],
                                    [-A[1, 0],  A[1, 1],  A[1, 2]]])
    return rotation_matrix_sat

def draw_bounding_box(cvImg, R, T, bbox, intrinsics, color, thickness):
    # color = (0,255,0)
    rep = np.matmul(intrinsics, np.matmul(R, bbox.T) + T)
    x = np.int32(rep[0]/rep[2] + 0.5)
    y = np.int32(rep[1]/rep[2] + 0.5)
    bbox_lines = [0, 1, 0, 2, 0, 4, 5, 1, 5, 4, 6, 2, 6, 4, 3, 2, 3, 1, 7, 3, 7, 5, 7, 6]
    for i in range(12):
        id1 = bbox_lines[2*i]
        id2 = bbox_lines[2*i+1]
        cvImg = cv2.line(cvImg, (x[id1],y[id1]), (x[id2],y[id2]), color, thickness=thickness, lineType=cv2.LINE_AA)
    return cvImg

def draw_pose_axis(cvImg, R, T, bbox, intrinsics, thickness):
    radius = np.linalg.norm(bbox, axis=1).mean()
    aPts = np.array([[0,0,0],[0,0,radius],[0,radius,0],[radius,0,0]])
    rep = np.matmul(intrinsics, np.matmul(R, aPts.T) + T)
    x = np.int32(rep[0]/rep[2] + 0.5)
    y = np.int32(rep[1]/rep[2] + 0.5)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[1],y[1]), (255,0,0), thickness=thickness, lineType=cv2.LINE_AA)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[2],y[2]), (0,255,0), thickness=thickness, lineType=cv2.LINE_AA)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[3],y[3]), (0,0,255), thickness=thickness, lineType=cv2.LINE_AA)
    return cvImg

def remap_points_to_origin_resolution(points_2d, transform_matrix):
    '''
    Remap 2d points on crop and resized patch to original image.
    '''
    num_points = len(points_2d)
    homo_points_2d = np.concatenate([points_2d, np.ones((num_points, 1))], axis=-1)
    inverse_transform_matrix = np.linalg.inv(transform_matrix)
    remapped_points_2d = np.matmul(inverse_transform_matrix[:2, :], homo_points_2d.transpose(), dtype=np.float32).transpose()
    return remapped_points_2d


######## main #########



key_points_3d_dir = 'bbox_3mdl1219_scaled.json'

data_root = 'Eutelsat-117_data'
# data_root = 'data/502/datasets_bop_240113/GSSAP_mini/GSSAP_mini'
save_root = 'log/im_result_quant/'

jsons_dir = data_root + '/json/'
masks_dir = data_root + '/label_color/'
imgs_dir = data_root + '/src/'     # src or gray
imgs_lists = sorted(os.listdir(imgs_dir))

pose_imput_size = 512
camera_K = np.array([716197.128, 0, 500, 0, 716197.128, 500, 0, 0, 1], dtype=np.float32).reshape(3,3)

with open(key_points_3d_dir, 'r') as f:
    points_3d_lib = json.load(f)

class_label = np.array([1])   # 5 sat: 1 2 3 4 5


for i, img_dir in enumerate(imgs_lists):

    # path
    img_path = os.path.join(imgs_dir, img_dir)
    img_id = img_dir.split(".")[0]
    json_path = jsons_dir + img_id + '.json'
    mask_path = masks_dir + img_id + '.png'
    img_name = os.path.join(*img_path.rsplit('/', 3)[1:])
    save_path = save_root +  img_name

    # image
    image = Image.open(img_path)

    # comput bbox
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    non_zero_pixels = cv2.findNonZero(mask)
    x, y, w, h = cv2.boundingRect(non_zero_pixels)
    bbox = [x, y, w, h]


    sat_id = class_label * 2 - 2   # 0 2 4 6 8 10
    cam_id = class_label * 2 - 1   # 1 3 5 7 9

    # crop bbox
    # bbox = [400, 400, 200, 200]
    size_ratio = 1.1    # random.uniform(0.8, 1.2)
    x1, y1, bbox_w, bbox_h = x, y, w, h
    xc, yc = x1 + bbox_w/2, y1 + bbox_h/2
    bbox_len = max(bbox_w, bbox_h) * size_ratio
    bbox[0], bbox[1], bbox[2], bbox[3] = xc - bbox_len/2, yc - bbox_len/2, bbox_len, bbox_len
    cropped_image = image.crop((bbox[0], bbox[1], bbox[0] + bbox[2], bbox[1] + bbox[3]))    # x1 y1 x2 y2
    resized_image = cropped_image.resize((pose_imput_size, pose_imput_size), Image.BILINEAR)
    img = np.array(resized_image).astype(np.float32)
    img /= 255.0
    # img = np.array(resized_image.convert("RGB")).astype(np.float32).transpose(2, 0, 1)
    img = np.expand_dims(img, axis=0)
    img = np.expand_dims(img, axis=0)
    # img = np.concatenate([img, img, img], axis=1)

    img.tofile('getoutput.bin')
    onnx_model = onnxruntime.InferenceSession('wdr_quant_output.onnx')
    onnx_outputs = list(map(lambda x:x.name, onnx_model.get_outputs()))
    output = onnx_model.run(onnx_outputs, {'img': img, 'img_metas': np.array([0], dtype=np.int64)})
    onnx_outputs_result = [x.name for x in onnx_model.get_outputs()]
    ort_outs = collections.OrderedDict(zip(onnx_outputs_result, output))    
    for i in ort_outs.keys():
        print(i)
        if (("quantized" in i)):
            print(ort_outs[i].shape)

            # 仅查看结果
            # 保存结果到文件中
            file_name = i.replace('/', '_')
            if '::' in i:
                file_name = file_name.replace('::', '_')
            # result_file_path = './model_parameter/yolov5_relu/output_result_file/'+ file_name
            result_file_path = './onnx_model/output_result_file/'+ file_name
            ort_len = len(ort_outs[i].shape)
            with open(result_file_path, 'wb') as outfile:
                if ort_len == 0:
                    continue
                for data1 in ort_outs[i]:
                    if ort_len == 1:
                        a = struct.pack('b', data1)
                        outfile.write(a)
                        continue
                    for data2 in data1:
                        if ort_len == 2:
                            a = struct.pack('b', data2)  # 量化后的结果应该为uint8类型
                            outfile.write(a)
                            continue
                        for data3 in data2:
                            if ort_len == 3:
                                a = struct.pack('b', data3)  # 量化后的结果应该为uint8类型
                                outfile.write(a)
                                continue
                            for data4 in data3:
                                if ort_len == 4:
                                    a = struct.pack('b', data4)  # 量化后的结果应该为uint8类型
                                    outfile.write(a)
                                    continue
                                for data5 in data4:
                                    if ort_len == 5:
                                        a = struct.pack('b', data5)  # 量化后的结果应该为uint8类型
                                        outfile.write(a)
                                        continue
                                    for data6 in data5:
                                        if ort_len == 6:
                                            a = struct.pack('b', data6)  # 量化后的结果应该为uint8类型
                                            outfile.write(a)
                                            continue
            outfile.close()