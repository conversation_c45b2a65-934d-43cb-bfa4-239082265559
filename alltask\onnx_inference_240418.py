import cv2
import numpy as np
import json
import onnxruntime
import os 
import glob
from PIL import Image
import random
import copy
from collections import OrderedDict
from scipy.spatial.transform import Rotation

def rodrigues_to_euler(R):
    """
    将旋转向量转换为欧拉角 (yaw, pitch, roll)，以 ZYX 顺序为例。
    :param rvec: 旋转向量 (3,)
    :return: 欧拉角 (yaw, pitch, roll)
    """
    # 将旋转向量转换为旋转矩阵

    # 计算 pitch
    sy = np.sqrt(R[0, 0]**2 + R[1, 0]**2)
    singular = sy < 1e-6  # 判断是否接近万向节锁死

    if not singular:
        yaw = np.arctan2(R[2, 1], R[2, 2])  # Yaw
        pitch = np.arctan2(-R[2, 0], sy)    # Pitch
        roll = np.arctan2(R[1, 0], R[0, 0]) # Roll
    else:
        yaw = np.arctan2(-R[1, 2], R[1, 1])
        pitch = np.arctan2(-R[2, 0], sy)
        roll = 0

    return np.degrees([yaw, pitch, roll])  # 转换为角度


def convert_sat_matrix(rotation_matrix_sat):
    A = rotation_matrix_sat
    rotation_matrix_sat = np.array([[ A[0, 0], -A[0, 1], -A[0, 2]],
                                    [ A[2, 0], -A[2, 1], -A[2, 2]],
                                    [-A[1, 0],  A[1, 1],  A[1, 2]]])
    return rotation_matrix_sat

def draw_bounding_box(cvImg, R, T, bbox, intrinsics, color, thickness):
    # color = (0,255,0)
    rep = np.matmul(intrinsics, np.matmul(R, bbox.T) + T)
    x = np.int32(rep[0]/rep[2] + 0.5)
    y = np.int32(rep[1]/rep[2] + 0.5)
    bbox_lines = [0, 1, 0, 2, 0, 4, 5, 1, 5, 4, 6, 2, 6, 4, 3, 2, 3, 1, 7, 3, 7, 5, 7, 6]
    for i in range(12):
        id1 = bbox_lines[2*i]
        id2 = bbox_lines[2*i+1]
        cvImg = cv2.line(cvImg, (x[id1],y[id1]), (x[id2],y[id2]), color, thickness=thickness, lineType=cv2.LINE_AA)
    return cvImg

def draw_pose_axis(cvImg, R, T, bbox, intrinsics, thickness):
    radius = np.linalg.norm(bbox, axis=1).mean()
    aPts = np.array([[0,0,0],[0,0,radius],[0,radius,0],[radius,0,0]])
    rep = np.matmul(intrinsics, np.matmul(R, aPts.T) + T)
    x = np.int32(rep[0]/rep[2] + 0.5)
    y = np.int32(rep[1]/rep[2] + 0.5)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[1],y[1]), (255,0,0), thickness=thickness, lineType=cv2.LINE_AA)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[2],y[2]), (0,255,0), thickness=thickness, lineType=cv2.LINE_AA)
    cvImg = cv2.line(cvImg, (x[0],y[0]), (x[3],y[3]), (0,0,255), thickness=thickness, lineType=cv2.LINE_AA)
    return cvImg

def remap_points_to_origin_resolution(points_2d, transform_matrix):
    '''
    Remap 2d points on crop and resized patch to original image.
    '''
    num_points = len(points_2d)
    homo_points_2d = np.concatenate([points_2d, np.ones((num_points, 1))], axis=-1)
    inverse_transform_matrix = np.linalg.inv(transform_matrix)
    remapped_points_2d = np.matmul(inverse_transform_matrix[:2, :], homo_points_2d.transpose(), dtype=np.float32).transpose()
    return remapped_points_2d


######## main #########

pose_model_dir = 'wdr.onnx'
pose_model_dir_int8 = 'wdr_quant_output.onnx'
pose_model_dir_int8_after = 'wdr_after.onnx'


key_points_3d_dir = 'bbox_3mdl1219_scaled.json'

data_root = 'Eutelsat-117_data'
# data_root = 'data/502/datasets_bop_240113/GSSAP_mini/GSSAP_mini'
save_root = 'log/im_result_cmodel/'

jsons_dir = data_root + '/json/'
masks_dir = data_root + '/label_color/'
imgs_dir = data_root + '/src/'     # src or gray
imgs_lists = sorted(os.listdir(imgs_dir))

pose_imput_size = 512
camera_K = np.array([716197.128, 0, 500, 0, 716197.128, 500, 0, 0, 1], dtype=np.float32).reshape(3,3)

with open(key_points_3d_dir, 'r') as f:
    points_3d_lib = json.load(f)

class_label = np.array([1])   # 5 sat: 1 2 3 4 5
pose_model = onnxruntime.InferenceSession(pose_model_dir)
pose_model_int8 = onnxruntime.InferenceSession(pose_model_dir_int8)
pose_model_int8_after = onnxruntime.InferenceSession(pose_model_dir_int8_after)

for i, img_dir in enumerate(imgs_lists):

    # path
    img_path = os.path.join(imgs_dir, img_dir)
    img_id = img_dir.split(".")[0]
    json_path = jsons_dir + img_id + '.json'
    mask_path = masks_dir + img_id + '.png'
    img_name = os.path.join(*img_path.rsplit('/', 3)[1:])
    save_path = save_root +  img_name

    # image
    image = Image.open(img_path)

    # comput bbox
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    non_zero_pixels = cv2.findNonZero(mask)
    x, y, w, h = cv2.boundingRect(non_zero_pixels)
    bbox = [x, y, w, h]


    sat_id = class_label * 2 - 2   # 0 2 4 6 8 10
    cam_id = class_label * 2 - 1   # 1 3 5 7 9

    # crop bbox
    # bbox = [400, 400, 200, 200]
    size_ratio = 1.1    # random.uniform(0.8, 1.2)
    x1, y1, bbox_w, bbox_h = x, y, w, h
    xc, yc = x1 + bbox_w/2, y1 + bbox_h/2
    bbox_len = max(bbox_w, bbox_h) * size_ratio
    bbox[0], bbox[1], bbox[2], bbox[3] = xc - bbox_len/2, yc - bbox_len/2, bbox_len, bbox_len
    cropped_image = image.crop((bbox[0], bbox[1], bbox[0] + bbox[2], bbox[1] + bbox[3]))    # x1 y1 x2 y2
    resized_image = cropped_image.resize((pose_imput_size, pose_imput_size), Image.BILINEAR)
    img = np.array(resized_image).astype(np.float32)
    img /= 255.0
    # img = np.array(resized_image.convert("RGB")).astype(np.float32).transpose(2, 0, 1)
    img = np.expand_dims(img, axis=0)
    img = np.expand_dims(img, axis=0)
    # img = np.concatenate([img, img, img], axis=1)

    img.tofile('test.bin')
    #------------- compute transform matrixs -------------------
    # 1. crop
    crop_x1, crop_x2 = int(xc - bbox_len/2), int(xc + bbox_len/2)
    crop_y1, crop_y2 = int(yc - bbox_len/2), int(yc + bbox_len/2)
    transform_matrix_1 = np.array([[1., 0, -crop_x1],
                                  [0, 1., -crop_y1],
                                  [0, 0, 1.]], dtype=np.float32)
    # 2. resize
    scale_factors = []
    scale_factor = pose_imput_size / bbox_len
    transform_matrix_2 = np.array([[scale_factor, 0, 0],
                                    [0, scale_factor, 0],
                                    [0, 0, 1.]], dtype=np.float32)
    w_scale, h_scale = scale_factor, scale_factor
    scale_factors = np.array([w_scale, h_scale, w_scale, h_scale], dtype=np.float32)

    transform_matrix = np.matmul(transform_matrix_2, transform_matrix_1)
    new_intrisic = np.matmul(transform_matrix, np.array(camera_K))


    sat_iden = np.array(sat_id, dtype=np.int64)
    # use float
    # pose_result = pose_model.run(['sat_keypoints_2d', 'cam_keypoints_2d'], {'img': img, 'img_metas': sat_iden})

    # use int + float
    pose_result_int8_before_quant = pose_model_int8.run(['onnx::Shape_750', 'onnx::Shape_951', 'onnx::Shape_887'], {'img': img, 'img_metas': sat_iden})
    pose_result = pose_model_int8_after.run(['sat_keypoints_2d', 'cam_keypoints_2d'],{'onnx::Shape_750': pose_result_int8_before_quant[0], 'onnx::Shape_951':pose_result_int8_before_quant[1], 'onnx::Shape_887':pose_result_int8_before_quant[2], 'img_metas': sat_iden})

    #use c_model
    # shape750 = np.fromfile("onnx_Shape_750", dtype=np.float32).reshape(1, 6,8,2,32,32)
    # shape951 = np.fromfile("onnx_Shape_951", dtype=np.float32).reshape(1,1024, 6)
    # shape887 = np.fromfile("onnx_Shape_887", dtype=np.float32).reshape(1, 6,8,2,32,32)

    # print("GROUP1")
    # print(pose_result_int8_before_quant[0])
    # print(shape750)

    # print("GROUP2")
    # print(pose_result_int8_before_quant[1])
    # print(shape951)

    # print("GROUP3")
    # print(pose_result_int8_before_quant[2])
    # print(shape887)


    # pose_result = pose_model_int8_after.run(['sat_keypoints_2d', 'cam_keypoints_2d'],{'onnx::Shape_750': shape750, 'onnx::Shape_951':shape951, 'onnx::Shape_887':shape887, 'img_metas': sat_iden})







    sat_points_2d = pose_result[0]
    cam_points_2d = pose_result[1]

    n = sat_points_2d.shape[0]
    sat_points_2d = sat_points_2d.reshape(-1,2)
    cam_points_2d = cam_points_2d.reshape(-1,2)

    #------- remap --------
    sat_points_2d = remap_points_to_origin_resolution(sat_points_2d, transform_matrix)
    cam_points_2d = remap_points_to_origin_resolution(cam_points_2d, transform_matrix)


    points_3d_sat = np.array(points_3d_lib[sat_id[0]], dtype=np.float32)
    points_3d_cam = np.array(points_3d_lib[cam_id[0]], dtype=np.float32)
    points_3d_sat = np.tile(points_3d_sat, (n, 1))
    points_3d_cam = np.tile(points_3d_cam, (n, 1))


    retval, sat_rotation_pred, sat_translation_pred, inliers = cv2.solvePnPRansac(
            points_3d_sat, 
            sat_points_2d,
            camera_K,
            None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=5, iterationsCount=500
        )
    sat_R, _ = cv2.Rodrigues(sat_rotation_pred)

    euler_angles = rodrigues_to_euler(sat_R)
    print("Euler Angles (yaw, pitch, roll):", euler_angles)   



    retval, cam_rotation_pred, cam_translation_pred, inliers = cv2.solvePnPRansac(
            points_3d_cam, 
            cam_points_2d,
            camera_K,
            None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=5, iterationsCount=500
        )
    cam_R, _ = cv2.Rodrigues(cam_rotation_pred)

    # comput error
    # with open(json_path, 'r', encoding='utf-16') as f:
    with open(json_path, 'r') as f:
        data = json.load(f)
    SatRot_X = data['Location']['SatRot']['X']
    SatRot_Y = data['Location']['SatRot']['Y']
    SatRot_Z = data['Location']['SatRot']['Z']

    CamRot_X = data['Location']['CamRot']['X']
    CamRot_Y = data['Location']['CamRot']['Y']
    CamRot_Z = data['Location']['CamRot']['Z']

    predR = sat_R

    predSat_cam_converted = np.array([[predR[0, 0], -predR[0, 1], -predR[0, 2]],
                                    [-predR[2, 0], predR[2, 1], predR[2, 2]],
                                    [ predR[1, 0], -predR[1, 1], -predR[1, 2]]])

    euler_predR_converted = np.degrees(Rotation.from_matrix(predSat_cam_converted).as_euler('xyz'))

    dis = abs((SatRot_Z - euler_predR_converted[2] + 180) % 360 - 180)

    print(img_id, "z_error:", dis)
    print("img_id:", img_id, "sat_R:", sat_R)
    print("sat_T", sat_translation_pred)
    print("\n")
    # sat_translation_pred = np.array([0, 0, -1]).reshape(3, 1)
    # img_path = 'data'
    # image = draw_pose_result(img_path, sat_R, sat_translation_pred, save_path)
    # image = draw_pose_result(save_path, gt_rotation, sat_translation_pred, save_path, gt=True)
    cvImg = cv2.imread(img_path)
    # cvImg = cv2.cvtColor(cvImg.astype(np.uint8), cv2.COLOR_RGB2BGR)
    cvImg = draw_pose_axis(cvImg, sat_R, sat_translation_pred, points_3d_sat[:8, ], camera_K, 1)
    cvImg = draw_bounding_box(cvImg, sat_R, sat_translation_pred, points_3d_sat[:8, ], camera_K, (0, 255, 255), 2)
    os.makedirs(save_path.rsplit("\\", 1)[0], exist_ok=True)
    cv2.imwrite(save_path, cvImg)