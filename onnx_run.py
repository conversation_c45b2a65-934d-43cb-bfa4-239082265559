import onnxruntime as ort
import numpy as np
import onnx

# 加载并检查模型的实际量化参数
model = onnx.load("qlinear_add.onnx")
print("=== 模型中的实际量化参数 ===")
for initializer in model.graph.initializer:
    if 'scale' in initializer.name or 'zero_point' in initializer.name:
        from onnx import numpy_helper
        value = numpy_helper.to_array(initializer)
        print(f"{initializer.name}: {value}")

# 加载 ONNX 模型
session = ort.InferenceSession(
    "qlinear_add.onnx",
    providers=["CPUExecutionProvider"]
)

# 输入数据
A = np.array([-114], dtype=np.int8)
B = np.array([-112], dtype=np.int8)

# 运行推理
output = session.run(None, {"A": A, "B": B})

# 输出结果
print("ONNX Runtime 输出 (int8):", output[0])
print("十六进制:", hex(output[0][0] & 0xFF))
print("反量化后的值:", (output[0].astype(np.float32) - (-105)) * 0.0365859717130661)
