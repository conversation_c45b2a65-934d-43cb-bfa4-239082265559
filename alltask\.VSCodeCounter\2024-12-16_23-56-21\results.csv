"filename", "language", "Python", "JSON", "comment", "blank", "total"
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000000.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000001.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000002.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000003.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000004.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\Eutelsat-117_data\json\Eutelsat117WestB_c1_5km_L0_180_000005.json", "JSON", 0, 198, 0, 0, 198
"d:\PROJECT\CASC502\4-Float_Code\alltask\bbox_3mdl1219_scaled.json", "JSON", 0, 254, 0, 0, 254
"d:\PROJECT\CASC502\4-Float_Code\alltask\bbox_5mdl_scaled_pad.json", "JSON", 0, 2522, 0, 0, 2522
"d:\PROJECT\CASC502\4-Float_Code\alltask\camera_K.json", "JSON", 0, 1, 0, 0, 1
"d:\PROJECT\CASC502\4-Float_Code\alltask\inference_pth_est_20240417.py", "Python", 65, 0, 8, 23, 96
"d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_240418.py", "Python", 174, 0, 40, 61, 275
"d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_alltask_float.py", "Python", 153, 0, 37, 42, 232
"d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_alltask_quant.py", "Python", 149, 0, 39, 45, 233
"d:\PROJECT\CASC502\4-Float_Code\alltask\onnx_inference_getoutput.py", "Python", 134, 0, 13, 24, 171
"d:\PROJECT\CASC502\4-Float_Code\alltask\pose.py", "Python", 75, 0, 4, 17, 96
"d:\PROJECT\CASC502\4-Float_Code\alltask\pose_vis.py", "Python", 147, 0, 5, 22, 174
"d:\PROJECT\CASC502\4-Float_Code\alltask\seg_utils.py", "Python", 60, 0, 118, 29, 207
"d:\PROJECT\CASC502\4-Float_Code\alltask\seg_vis.py", "Python", 62, 0, 3, 17, 82
"d:\PROJECT\CASC502\4-Float_Code\alltask\tools\visualize.py", "Python", 462, 0, 17, 76, 555
"d:\PROJECT\CASC502\4-Float_Code\alltask\yolov5_utils.py", "Python", 137, 0, 52, 29, 218
"Total", "-", 1618, 3965, 336, 385, 6304