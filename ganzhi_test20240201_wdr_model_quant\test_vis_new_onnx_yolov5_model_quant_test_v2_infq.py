import os, time
import numpy as np
from PIL import Image
from seg_utils import rawOutput2SegMap, segMapResize, componentFeature, calRealSize, imageResize
from yolov5_utils import filter_box, im_crop, draw_det_box
from seg_vis import draw_points, save_results, draw_seg_box
import onnxruntime
from pathlib import Path
import cv2
import json
from pose_vis import draw_pose_result

# onnx quantation
from onnxruntime.quantization import CalibrationDataReader, QuantFormat, quantize_static, QuantType, CalibrationMethod
import onnxruntime.quantization.shape_inference as onnx_shape

def get_calibration_data():
# def get_calibration_data(yolov5_cdata):
# def get_calibration_data(model_name):
    # add by luis
    crop_resize_img_pose_all = []
    sat_iden_all = []
    crop_resize_img_rec_all = []
    # yolov5_sate_gray_640
    img_all = []
    crop_resize_img_com_all = []

    # 参数设置
    det_sate_input_size = 640  # 整星检测输入尺寸
    det_component_input_size=640  # 部件检测输入尺寸
    seg_input_size = 576  # 部件分割输入尺寸
    reg_identity_input_size=640  # 身份识别输入尺寸
    pose_estimation_size = 512   # 姿态估计输入尺寸

    base_save_path = './log/im_result'
    os.makedirs(base_save_path, exist_ok=True)

    key_points_3d_dir ='./bbox_5mdl_scaled_pad.json'
    camera_K_dir  ='./camera_K.json'

    CLASSES_sate = ['satellite']
    CLASSES_component = ['panel', 'aerial', 'nozzle', 'optical', 'holder', 'sensor', 'docking', 'main', 'arm', 'load',
                         'aerial-pole', 'aerial-unknown', 'others']

    COLOR_sate =[(255,0, 0)]

    COLOR_component = [(255,0,0), (0,255,0),(255,255,0),(0,0,255),(255,0,255),(0,0,124),(202,202,202),(255,255,255),
                       (124,0,0),(0,124,0),(124,124,0),(0,124,124),(0,255,255)]


    # 定义模型
    det_sate_model = onnxruntime.InferenceSession('./onnx_model/yolov5_sate_gray_{}.onnx'.format(det_sate_input_size))

    # det_sate_model = onnxruntime.InferenceSession('./onnx_model/yolov5_gray_Relu_640.onnx')
    det_component_model = onnxruntime.InferenceSession('./onnx_model/yolov5_gray_{}.onnx'.format(det_component_input_size))

    # det_component_model = onnxruntime.InferenceSession('./onnx_model/yolov5_gray_Relu_640.onnx')
    reg_identity_model = onnxruntime.InferenceSession('./onnx_model/sat_recog_model_last.onnx')
    seg_component_model = onnxruntime.InferenceSession('./onnx_model/upernet_mobilenet_{}.onnx'.format(seg_input_size))
    pose_model = onnxruntime.InferenceSession('./onnx_model/end2end0117.onnx')

    # 读取测试数据
    data_base_path = './dummy_data'
    data_names = os.listdir(os.path.join(data_base_path, 'src'))

    im_ids = []
    images = []
    labels = []
    for ii, line in enumerate(data_names):
        _image = os.path.join(data_base_path, 'src', line)
        _cat = os.path.join(data_base_path, 'label', line)
        assert os.path.isfile(_image)
        assert os.path.isfile(_cat)

        im_ids.append(str(Path(line).stem))
        images.append(_image)
        labels.append(_cat)

    results = {'sate_coord': [], 'sate_id': {}, 'sate_comp': [], 'sate_pose': {}}

    for image, label, im_id in zip(images, labels, im_ids):
        # st_time = time.time()
        # -----------------------图片读取与预处理-----------------------------
        # 读取图片和分割标签
        _img = Image.open(image).convert('L')  # 单通道
        _target = Image.open(label)  # 分割图

        img_gray = np.array(_img).astype(np.uint8)

        # 图片尺寸变换
        img = cv2.resize(img_gray.astype(np.float32), (det_sate_input_size, det_sate_input_size))
        # 图片像素归一化
        img /= 255.0
        # 图片通道扩展
        img = np.expand_dims(img, axis=0)
        img = np.expand_dims(img, axis=0)

        # ---------------------------------------------------------------
        ### ********************************
        # onnx model quant
        # if (model_name == 'yolov5_sate_gray_640'):
        img_all.append(img)
        ### ********************************
        
        if (im_id == "GSSAP_c1_d0_L0_000001"):
            pred = det_sate_model.run(['output'], {'images': img})[0]
            # pred = yolov5_cdata
            # 整星边界框NMS过滤与合并
            outbox = filter_box(pred, 0.5, 0.5)
        else:
            continue






        # -------------------------------整星检测--------------------------
        # pred = det_sate_model.run(['output'], {'images': img})[0]
        # 整星边界框NMS过滤与合并
        outbox = filter_box(pred, 0.5, 0.5)
        # ---------------------------------------------------------------

        if outbox.shape[0] > 0:
            # ------------根据检测整星结果对目标所在区域进行图像切片-----------
            rate_w = img_gray.shape[-1] / img.shape[-1]
            rate_h = img_gray.shape[-2] / img.shape[-2]
            crop_coor = im_crop(outbox, [img_gray.shape[-2], img_gray.shape[-1]], rate_h, rate_w)
            img_crop_gray = img_gray[crop_coor[1]:crop_coor[3]+1, crop_coor[0]:crop_coor[2]+1]
            crop_size = [img_crop_gray.shape[-1], img_crop_gray.shape[-2]]
            results['sate_coord'].append(crop_coor)
            # 保存整星切片图像，用于验证，实际不需要
            cv2.imwrite(os.path.join(base_save_path, '{}_crop.png'.format(im_id)), img_crop_gray)
            img_sate_det_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
            img_sate_det_vis = draw_det_box(img_sate_det_vis, outbox, CLASSES_sate, COLOR_sate, rate_h, rate_w)
            cv2.imwrite(os.path.join(base_save_path, '{}_sate_det_vis.png'.format(im_id)), img_sate_det_vis)
            # ---------------------------------------------------------------------

            # -----------------------身份识别-----------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_rec = cv2.resize(img_crop_gray.astype(np.float32), (reg_identity_input_size, reg_identity_input_size))
            crop_resize_img_rec /= 255
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)
            crop_resize_img_rec = np.expand_dims(crop_resize_img_rec, axis=0)


            crop_resize_img_rec_all.append(crop_resize_img_rec)
            # crop_resize_img_rec.tofile('input125.bin')
            # return crop_resize_img_rec_all


            # 输入分类网络模型
            output_class = reg_identity_model.run(['output_cls'], {'input': crop_resize_img_rec})[0]
            # 从网络输出的类别得分中取出实际存在的类型得分（前者大于后者，因为考虑后续任务对类别数量的拓展性，这里实际存在类别数量暂定为19）
            output_class_real_c = output_class[:, 0:19]
            # softmax
            output_class_real_c_exp = np.exp(output_class_real_c)
            output_class_real_c_exp = output_class_real_c_exp / np.sum(output_class_real_c_exp, axis=1, keepdims=True)
            # 在从有效类别得分中取出任务场景相关的类别，可能会根据场景设定不同类别子集，这里以取前5类为例
            output_class_temp = output_class_real_c_exp[:, 0:5]
            # 获取身份id下标和置信度得分
            pred_class = np.argmax(output_class_temp, axis=1)
            pred_class_p = np.max(output_class_temp, axis=1)
            results['sate_id']['id'] = pred_class
            results['sate_id']['conf'] = pred_class_p
            # ------------------------------------------------------------------------------

           

            # -----------------------部件检测----------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_com = cv2.resize(img_crop_gray.astype(np.float32), (det_component_input_size, det_component_input_size))
            crop_resize_img_com /= 255
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            crop_resize_img_com = np.expand_dims(crop_resize_img_com, axis=0)
            # 图像输入网络
            pred_com = det_component_model.run(['output'], {'images': crop_resize_img_com})[0]

            # crop_resize_img_com_all.append(crop_resize_img_com)
            # NMS
            outbox_com = filter_box(pred_com, 0.5, 0.5)
            if outbox_com.shape[0] > 0:
                results['sate_comp'].append(outbox_com)

                # 可视化，用于效果验证，实际运行不需要
                rate_w = img_crop_gray.shape[-1] / crop_resize_img_com.shape[-1]
                rate_h = img_crop_gray.shape[-2] / crop_resize_img_com.shape[-2]
                img_com_det_vis = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
                img_com_det_vis = draw_det_box(img_com_det_vis, outbox_com, CLASSES_component, COLOR_component, rate_h, rate_w)
                cv2.imwrite(os.path.join(base_save_path, '{}_com_det_vis.png'.format(im_id)), img_com_det_vis)
            else:
                print('No component detected.')
            # --------------------------------------------------------------------------

            # --------------------部件分割--------------------------------------------
            # 输入图片切片尺寸变换
            crop_resize_img_seg = cv2.resize(img_crop_gray.astype(np.float32), (seg_input_size, seg_input_size))
            crop_resize_img_seg /= 255
            crop_resize_img_seg = np.expand_dims(crop_resize_img_seg, axis=0)
            crop_resize_img_seg = np.expand_dims(crop_resize_img_seg, axis=0)
            seg = seg_component_model.run(['output_seg'], {'input': crop_resize_img_seg})[0]
            seg = seg[0]
            # 模型输出转分割图
            segmap = rawOutput2SegMap(seg)
            #分割图变换至原始尺寸
            output_segmap_orisize = segMapResize(segmap, crop_size)

            out_segmap = np.zeros_like(output_segmap_orisize)

            # 提取部件特征，包括误检测滤除，联通域提取，最小外接矩形提取
            out_segmap1, out_points1 = componentFeature(output_segmap_orisize.copy(), 'fanban')
            if out_segmap1 is not None:
                out_segmap += out_segmap1

            out_segmap2, out_points2 = componentFeature(output_segmap_orisize.copy(), 'tianxian')
            if out_segmap2 is not None:
                out_segmap += out_segmap2

            out_segmap3, out_points3 = componentFeature(output_segmap_orisize.copy(), 'xiangji')
            if out_segmap3 is not None:
                out_segmap += out_segmap3

            out_segmap4, out_points4 = componentFeature(output_segmap_orisize.copy(), 'benti')
            if out_segmap4 is not None:
                out_segmap += out_segmap4

            # 部件尺寸计算
            if out_points1 is not None:
                real_size1 = calRealSize(out_points1[:, 5:, :], 0.07)
            else:
                real_size1 = None

            if out_points2 is not None:
                real_size2 = calRealSize(out_points2[:, 5:, :], 0.07)
            else:
                real_size2 = None

            if out_points3 is not None:
                real_size3 = calRealSize(out_points3[:, 5:, :], 0.07)
            else:
                real_size3 = None

            if out_points4 is not None:
                real_size4 = calRealSize(out_points4[:, 5:, :], 0.07)
            else:
                real_size4 = None

            out_points = {1: [out_points1, real_size1], 2: [out_points2, real_size2], 3: [out_points3, real_size3],
                          4: [out_points4, real_size4]}
            # ed_time = time.time()

            # 以下都是可视化和数据保存的内容，实际运行不需要
            img_seg_vis_ori = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
            img_seg_vis = np.stack([img_crop_gray, img_crop_gray, img_crop_gray], axis=2)
            image_dp = draw_points(img_seg_vis, out_points)
            image_dp = image_dp[:, :, ::-1]
            save_results(im_id, img_seg_vis_ori, out_segmap, image_dp, base_save_path)
            # ----------------------------------------------------------------------------
            # -----------------------相对姿态估计--------------------------------------------
            # 读取模型以及相机三维点以及相机内参，用于相对姿态解算
            with open(key_points_3d_dir, 'r') as f:
                points_3d_lib = json.load(f)
            with open(camera_K_dir, 'r') as f:
                camera_K = json.load(f)
            # 输入图像尺寸变换
            crop_resize_img_pose = cv2.resize(img_crop_gray.astype(np.float32), (pose_estimation_size, pose_estimation_size))
            crop_resize_img_pose /= 255
            # crop_resize_img_pose = np.stack([crop_resize_img_pose, crop_resize_img_pose, crop_resize_img_pose], axis=0)
            crop_resize_img_pose = np.expand_dims(crop_resize_img_pose, axis=0)
            crop_resize_img_pose = np.expand_dims(crop_resize_img_pose, axis=0)
            # with open('wdr_input.bin', 'wb')as fp:
            #     for data in crop_resize_img_pose:
            #         for data1 in data:
            #             for data2 in data1:
            #                 for data3 in data2:
            #                     #a = struct.pack('B', x)
            #                     a = struct.pack('f', data3)
            #                     fp.write(a)
            crop_resize_img_pose_all.append(crop_resize_img_pose)
            return crop_resize_img_pose_all
            # 根据前面识别的目标身份id，变换至目标三维点读取下标
            pred_class_pose = pred_class[0]
            pred_class_id = pred_class_pose + 1
            sat_id = 2 * pred_class_id - 2
            cam_id = 2 * pred_class_id - 1
            sat_iden = np.array([sat_id], dtype=np.int64)
            # 图像输入姿态估计网络，得到预测的模型和相机关键点二维坐标
            pose_result = pose_model.run(['sat_keypoints_2d', 'cam_keypoints_2d'], {'img': crop_resize_img_pose, 'img_metas': sat_iden})
            sat_points_2d = pose_result[0]
            cam_points_2d = pose_result[1]

            if sat_points_2d.shape[0] > 0:
                # pnp 解算姿态
                n = sat_points_2d.shape[0]
                sat_points_2d = sat_points_2d.reshape(-1, 2)
                cam_points_2d = cam_points_2d.reshape(-1, 2)

                points_3d_sat = np.array(points_3d_lib[sat_id], dtype=np.float64)
                points_3d_cam = np.array(points_3d_lib[cam_id], dtype=np.float64)
                points_3d_sat = np.tile(points_3d_sat, (n, 1))
                points_3d_cam = np.tile(points_3d_cam, (n, 1))

                camera_K = np.array(camera_K, dtype=np.float64)
                K = camera_K
                retval, sat_rotation_pred, sat_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_sat,
                    sat_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                sat_R, _ = cv2.Rodrigues(sat_rotation_pred)

                retval, cam_rotation_pred, cam_translation_pred, inliers = cv2.solvePnPRansac(
                    points_3d_cam,
                    cam_points_2d,
                    K,
                    None, flags=cv2.SOLVEPNP_ITERATIVE, reprojectionError=15, iterationsCount=100
                )
                cam_R, _ = cv2.Rodrigues(cam_rotation_pred)

                results['sate_pose']['sat_R'] = sat_R
                results['sate_pose']['cam_R'] = cam_R

                # 以下是可视化代码，用于验证，实际运行不需要
                sat_translation_pred[0] = 0
                sat_translation_pred[1] = 0
                sat_translation_pred[2] = 800000
                img_pose_vis = np.stack([img_gray, img_gray, img_gray], axis=2)
                save_path = os.path.join(base_save_path, '{}_pose_recog.png'.format(im_id))
                draw_pose_result(img_pose_vis, sat_R, sat_translation_pred, save_path)

            else:
                print('Unable to recognize target pose.')
            # -------------------------------------------------------------------------
            print('results: {}'.format(results))
        else:
            print('No target sate detect.')

##########################################################################################
class DataReader(CalibrationDataReader):
    def __init__(self):
        self.enum_data = None
        self.enum_data1 = None
        self.enum_data2 = None
        # get input data
        self.img = get_calibration_data()
        #self.x_input_all, self.d_input_all = get_calibration_data()
        #self.module_data  = get_calibration_data()
        #self.input_name = session.get_inputs()[0].name
        #self.datasize_1 = len(self.x_input_all)
        self.datasize_1 = len(self.img)
        self.datasize_2 = 0
        self.list_num = 0

        self.data_num = 0
        self.data_num1 = 0
        self.data_valid_flag = 0
        self.data_final = None

    def get_next(self):
        if self.enum_data is None:
            self.calibration_data = []
            for i in range(self.datasize_1):
                data1 = {'images': self.img[i]}
                self.calibration_data.append(data1)
            self.enum_data = iter(self.calibration_data)

        return next(self.enum_data, None)    


def yolov5_onnx_model_quantation():
    #----------------------------------------------------------------------
    ori_onnx_path = './onnx_model/yolov5_gray_640.onnx'
    out_onnx_path = './onnx_model/quant_onnx_model/yolov5_gray_640_quant_v3.onnx'
    #shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'

    # Quant

    # onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

    data_reader = DataReader()
    quantize_static(
        model_input = ori_onnx_path, # 输入模型
        model_output = out_onnx_path, # 输出模型
        calibration_data_reader = data_reader, # 校准数据读取器
        quant_format = QuantFormat.QOperator, # 量化格式 QDQ / QOperator
        #quant_format = QuantFormat.QDQ, # 量化格式 QDQ / QOperator
        activation_type = QuantType.QInt8, # 激活类型 Int8 / UInt8
        #weight_type = QuantType.QUInt8, # 参数类型 Int8 / UInt8
        weight_type = QuantType.QInt8, # 参数类型 Int8 / UInt8
        calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
        #calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
        #per_channel=True,
        #extra_options = extra_options,
        #optimize_model=False # 是否优化模型
    )
    
    print("quant finish")


# def yolov5_onnx_model_quantation_new():
#     print('==> Preparing data..')
#     #----------------------------------------------------------------------
#     ori_onnx_path = './onnx_model/handle_onnx_model/yolov5_gray_Relu_640_cnn.onnx'
#     out_onnx_path = './onnx_model/quant_onnx_model/yolov5_gray_Relu_640_cnn_quant_QDQ.onnx'
#     # shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'
#     # onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

#     #shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'
#     #onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

#     data_reader = DataReader()
#     quantize_static(
#         model_input = shape_onnx_path, # 输入模型
#         model_output = out_onnx_path, # 输出模型
#         calibration_data_reader = data_reader, # 校准数据读取器
#         quant_format = QuantFormat.QOperator, # 量化格式 QDQ / QOperator
#         activation_type = QuantType.QInt8, # 激活类型 Int8 / UInt8
#         weight_type = QuantType.QInt8, # 参数类型 Int8 / UInt8
#         calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
#         #per_channel=True,
#         # extra_options = extra_options,
#         # optimize_model=False # 是否优化模型
#     )
#     print("quant finish")


def yolov5_onnx_model_quantation_new2(ori_onnx_path, out_onnx_path):
    print('==> Preparing data..')
    #----------------------------------------------------------------------
    shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'
    onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

    #shape_onnx_path = os.path.splitext(os.path.basename(ori_onnx_path))[0] + '_shape.onnx'
    #onnx_shape.quant_pre_process(ori_onnx_path, shape_onnx_path)

    data_reader = DataReader()
    quantize_static(
        model_input = shape_onnx_path, # 输入模型
        model_output = out_onnx_path, # 输出模型
        calibration_data_reader = data_reader, # 校准数据读取器
        quant_format = QuantFormat.QOperator, # 量化格式 QDQ / QOperator
        activation_type = QuantType.QInt8, # 激活类型 Int8 / UInt8
        weight_type = QuantType.QInt8, # 参数类型 Int8 / UInt8
        calibrate_method = CalibrationMethod.MinMax, # 数据校准方法 MinMax / Entropy / Percentile
        #per_channel=True,
        # extra_options = extra_options,
        # optimize_model=False # 是否优化模型
    )
    print("quant finish")

import onnx
import copy
import collections
import struct
if __name__ == '__main__':
    #export_onnx()
    # model quant
    #wdr_onnx_model_quantation()
    #mobilenet_onnx_model_quantation()

    # ori_onnx_path = './onnx_model/yolov5_sate_gray_640.onnx'
    # out_onnx_path = './onnx_model/quant_onnx_model/yolov5_sate_gray_640_quant_v1.onnx'
    # onnx_model_quantation(ori_onnx_path, out_onnx_path)

    # yolov5_onnx_model_quantation_new()

    # yolov5_onnx_model_quantation_new2('./onnx_model/handle_onnx_model/yolov5_gray_Relu_640_cnn.onnx', 
    #                                   './onnx_model/quant_onnx_model/yolov5_gray_Relu_640_cnn_quant_class19.onnx')
    
    # yolov5_onnx_model_quantation_new2('./onnx_model/handle_onnx_model/yolov5_gray_640_cnn.onnx', 
                                    #   './onnx_model/quant_onnx_model/yolov5_gray_640_cnn_quant_v3.onnx')

    # # get in/out file
    
    img = get_calibration_data()
    img[0].tofile("input.bin")
    # img = np.fromfile('input.bin', dtype=np.float32).reshape(1,3,256,128)


    # onnx_model = onnxruntime.InferenceSession('./model_parameter/yolov5_relu/yolov5_gray_Relu_640_cnn_quant_class19_output.onnx')
    onnx_model = onnxruntime.InferenceSession('./sqy_onnx_model/wdr_onnx_model_240515_quant_new_0515_v6_output.onnx')
    onnx_outputs = list(map(lambda x:x.name, onnx_model.get_outputs()))
    output = onnx_model.run(onnx_outputs, {'img': img[0], 'img_metas': np.array([0], dtype=np.int64)})
    onnx_outputs_result = [x.name for x in onnx_model.get_outputs()]
    ort_outs = collections.OrderedDict(zip(onnx_outputs_result, output))    
    for i in ort_outs.keys():
        print(i)
        if (("quantized" in i)):
            print(ort_outs[i].shape)

            # 仅查看结果
            # 保存结果到文件中
            file_name = i.replace('/', '_')
            if '::' in i:
                file_name = file_name.replace('::', '_')
            # result_file_path = './model_parameter/yolov5_relu/output_result_file/'+ file_name
            result_file_path = './sqy_onnx_model/output_result_file/'+ file_name
            ort_len = len(ort_outs[i].shape)
            with open(result_file_path, 'wb') as outfile:
                if ort_len == 0:
                    continue
                for data1 in ort_outs[i]:
                    if ort_len == 1:
                        a = struct.pack('b', data1)
                        outfile.write(a)
                        continue
                    for data2 in data1:
                        if ort_len == 2:
                            a = struct.pack('b', data2)  # 量化后的结果应该为uint8类型
                            outfile.write(a)
                            continue
                        for data3 in data2:
                            if ort_len == 3:
                                a = struct.pack('b', data3)  # 量化后的结果应该为uint8类型
                                outfile.write(a)
                                continue
                            for data4 in data3:
                                if ort_len == 4:
                                    a = struct.pack('b', data4)  # 量化后的结果应该为uint8类型
                                    outfile.write(a)
                                    continue
                                for data5 in data4:
                                    if ort_len == 5:
                                        a = struct.pack('b', data5)  # 量化后的结果应该为uint8类型
                                        outfile.write(a)
                                        continue
                                    for data6 in data5:
                                        if ort_len == 6:
                                            a = struct.pack('b', data6)  # 量化后的结果应该为uint8类型
                                            outfile.write(a)
                                            continue
            outfile.close()
    # # 
    # encoder_input = './model_parameter/yolov5_relu/'+ 'input.bin'
    # with open(encoder_input, 'wb') as outfile:
    #     for data in img:
    #         for data2 in data:
    #             for data3 in data2:
    #                 for data4 in data3:
    #                     a = struct.pack('f', data4) # f: float32
    #                     outfile.write(a)
    # outfile.close()
# 
    # print("finish")


    # img = get_calibration_data()
    # onnx_model = onnxruntime.InferenceSession('onnx_model/yolov5_silu_onnx_model/yolov5_gray_640_cnn_q_output.onnx')
    # onnx_outputs = list(map(lambda x:x.name, onnx_model.get_outputs()))
    # output = onnx_model.run(onnx_outputs, {'images': img})
    # onnx_outputs_result = [x.name for x in onnx_model.get_outputs()]
    # ort_outs = collections.OrderedDict(zip(onnx_outputs_result, output))  

    # for i in ort_outs.keys():
        # print(i)
#         if (("quantized" in i)):
#         # if (("quantized" not in i)):
#             print(ort_outs[i].shape)
# # 
#             # 仅查看结果
#             # 保存结果到文件中
#             file_name = i.replace('/', '_')
#             result_file_path = './model_parameter/yolov5_silu/output_result_file_int8/'+ file_name
#             with open(result_file_path, 'wb') as outfile:
#                 for data1 in ort_outs[i]:
#                     for data2 in data1:
#                         for data3 in data2:
#                             for data4 in data3:
#                                 a = struct.pack('b', data4)  # 量化后的结果应该为uint8类型
#                                 # a = struct.pack('f', data4)  # 量化后的结果应该为uint8类型
#                                 outfile.write(a)
#             outfile.close()
        # if (("quantized" in i) and ("326" in i)):
        #     print(i)
        #     print(ort_outs[i].shape)
        #     file_name = i.replace('/', '_')
        #     result_file_path = './model_parameter/yolov5_silu/output_result_file_int8/'+ file_name
        #     arr_transposed = np.transpose(ort_outs[i], (0, 2, 3, 1))
        #     with open(result_file_path, 'wb') as outfile:
        #         for k in range(arr_transposed.shape[0]):
        #             for m in range(arr_transposed.shape[1]):
        #                 for z in range(arr_transposed.shape[2]):
        #                     for q in range(32):
        #                         if (q < arr_transposed.shape[3]):
        #                             data_out = arr_transposed[k][m][z][q]
        #                         else:
        #                             data_out = 0
        #                         a = struct.pack('b', data_out)  # 量化后的结果应该为uint8类型
        #                         outfile.write(a)

        #     outfile.close()

        # if (("quantized" in i) and ("images" in i)):
        #     print(ort_outs[i].shape)
        #     file_name = i.replace('/', '_')
        #     result_file_path = './model_parameter/yolov5_silu/output_result_file_int8_new/'+ file_name
        #     arr_transposed = np.transpose(ort_outs[i], (0, 2, 3, 1))
        #     with open(result_file_path, 'wb') as outfile:
        #         for k in range(arr_transposed.shape[0]):
        #             for m in range(arr_transposed.shape[1]):
        #                 for z in range(arr_transposed.shape[2]):
        #                     for q in range(32):
        #                         # if (q<18):
        #                         if (q < arr_transposed.shape[3]):
        #                             data_out = arr_transposed[k][m][z][q]
        #                         else:
        #                             data_out = 0
        #                         a = struct.pack('b', data_out)  # 量化后的结果应该为uint8类型
        #                         outfile.write(a)

        #     outfile.close()


    # input = './model_parameter/yolov5_silu/'+ 'input.bin'
    # with open(input, 'wb') as outfile:
    #     for data in img:
    #         for data2 in data:
    #             for data3 in data2:
    #                 for data4 in data3:
    #                     a = struct.pack('f', data4) # f: float32
    #                     outfile.write(a)
    # outfile.close()

    with open('./model_parameter/yolov5_silu/outputq8.bin', 'rb') as f:
        # 从文件中读取数据，并按照float类型解析
        yolov5_cdata = np.fromfile(f, dtype=np.float32)
        # 调整数据的形状为(1, 25200, 6)
        yolov5_cdata = yolov5_cdata.reshape(1, 25200, 6)

    print(yolov5_cdata.shape)


    get_calibration_data(yolov5_cdata)

    print("finish")




