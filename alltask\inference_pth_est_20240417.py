from tools.inference_estimator import init_estimator, inference_estimator
import numpy as np
from PIL import Image
import time
import json
import os
from tools.visualize import imshow_pose_axis
import cv2
from scipy.spatial.transform import Rotation
from transforms3d import euler

def draw_pose_result(img, R, T, out_file):
    
    radius = list([180.18])
    camera_params = np.array([716197.128, 0, 500, 0, 716197.128, 500, 0, 0, 1], dtype=np.float32).reshape(3,3)
    R = R.reshape(1, 3, 3)
    T = T.reshape(1, 3)
    label = np.array([0])
    out_file = 'inference_pose_results_0417.png'
    image = imshow_pose_axis(img=img, radius=radius, rotations=R, translations=T, labels=label, camera_k=camera_params, out_file=out_file, show = False)
    
    return image

def convert_sat_matrix(rotation_matrix_sat):
    A = rotation_matrix_sat
    rotation_matrix_sat = np.array([[ A[0, 0], -A[0, 1], -A[0, 2]],
                                    [ A[2, 0], -A[2, 1], -A[2, 2]],
                                    [-A[1, 0],  A[1, 1],  A[1, 2]]])
    return rotation_matrix_sat

def PoseEstimator():

    # get model info path
    est_config_file = 'configs/wdr_60class.py' # 'configs/wdr.py'
    est_checkpoint_file = 'test_for_onnx_240116/wdr_60class/iter_100000.pth'

    # initial model
    est_model = init_estimator(est_config_file, est_checkpoint_file, device='cuda:1')

    # get 3d keypoints
    points_3d = np.array(json.load(open('test_for_onnx_240116/bbox_5mdl_scaled_pad.json')), dtype=np.float32)

    # get img info path
    # data_root = 'data/502/datasets_bop_240113/GSSAP_mini/GSSAP_mini'
    data_root = 'data/502/datasets_bop_240113/dataset_0417sample/000001'
    jsons_dir = data_root + '/json/'
    masks_dir = data_root + '/label_color/'
    imgs_dir = data_root + '/src/'     # src or gray
    imgs_lists = sorted(os.listdir(imgs_dir))

    label = np.array([1])   # 5 sat: 1 2 3 4 5

    for img_dir in imgs_lists:
        img_path = os.path.join(imgs_dir, img_dir)
        img_id = img_dir.split(".")[0]
        json_path = jsons_dir + img_id + '.json'
        mask_path = masks_dir + img_id + '.png'

        # comput bbox
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        non_zero_pixels = cv2.findNonZero(mask)
        x, y, w, h = cv2.boundingRect(non_zero_pixels)
        bbox = np.array([[x, y, x + w, y + h]], dtype=np.float32)   # xyxy
        
        # inference
        sat_R, sat_T, cam_R, cam_T = inference_estimator(est_model, img_path, label, bbox, points_3d)

        # with open(json_path, 'r', encoding='utf-16') as f:
        with open(json_path, 'r') as f:
            data = json.load(f)
        SatRot_X = data['Location']['SatRot']['X']
        SatRot_Y = data['Location']['SatRot']['Y']
        SatRot_Z = data['Location']['SatRot']['Z']

        CamRot_X = data['Location']['CamRot']['X']
        CamRot_Y = data['Location']['CamRot']['Y']
        CamRot_Z = data['Location']['CamRot']['Z']

        predR = sat_R
        predSat_cam_converted = np.array([[-predR[1, 0], predR[1, 1], predR[1, 2]],
                                          [-predR[0, 0], predR[0, 1], predR[0, 2]],
                                          [-predR[2, 0], predR[2, 1], predR[2, 2]]])
        CamRot_matrix = euler.euler2mat(CamRot_X/180 * np.pi, CamRot_Y/180 * np.pi, CamRot_Z/180 * np.pi, 'sxyz')

        Sat_world_matrix = np.dot(CamRot_matrix, predSat_cam_converted)

        euler_predR_converted = np.degrees(Rotation.from_matrix(Sat_world_matrix).as_euler('xyz'))
        dis = abs((SatRot_Z - euler_predR_converted[2] + 180) % 360 - 180)

        print(img_id, "z_error:", dis)

if __name__ == '__main__':

    PoseEstimator()

