import os

import numpy as np
import cv2
from PIL import Image


def rawOutput2SegMap(raw_output):
    # 输入：分割网络输出的每个像素所属概率分布
    # 输出：每个像素所属类型
    seg_map = np.argmax(raw_output, axis=0)
    return seg_map

def segMapResize(segmap, size):
    # 输入：原始尺寸分割图，目标尺寸,类型(w, h)
    # 输出：尺寸变换后的分割图
    segmap_pil = Image.fromarray(segmap.astype('uint8'))
    re_segmap_pil = segmap_pil.resize(size=size, resample=Image.NEAREST)
    re_segmap = np.array(re_segmap_pil)
    return re_segmap

def imageResize(image, size):
    # 输入：原始尺寸图像，目标尺寸,类型(w, h)
    # 输出：尺寸变换后的图形
    image_pil = Image.fromarray(image.astype('uint8'))
    re_image_pil = image_pil.resize(size=size, resample=Image.BILINEAR)
    re_image = np.array(re_image_pil)
    return re_image

def componentFeature(segmap, comp_name):
    # 输入：segmap: 像素级部件分割结果, comp_name: 需要提取特征的部件类型
    # 输出：部件特征，如果输入包含有效的识别结果，返回一个list, 每个元素还是一个np数组，尺寸6*2
    # 包括部件质心像素座标，最小外接矩形的顶点座标，外接矩形宽高[[x_c, y_c], [x1, y1], [x2, y2], [x3, y3], [x4, y4], [w, h]]
    name2id = {'fanban': 1, 'tianxian': 2, 'xiangji': 3, 'benti': 4, 'zaiwei': 5, 'jixiebi': 6}

    comp_id = name2id[comp_name]
    comp_pixel_posi = np.sum(segmap == comp_id)

    # 待检测部件像素数过少
    if comp_pixel_posi < 30:
        return None, None

    w_ori, h_ori = segmap.shape
    n_total = w_ori*h_ori

    segmap[segmap != comp_id] = 0
    # segmap[segmap == comp_id] = 1

    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(image=segmap, connectivity=8, ltype=cv2.CV_32S)

    if stats.shape[0] == 0 or stats.shape[0] == 1:
        # 没有检测出指定的部件
        return None, None
    if stats.shape[0] == 2:  # 检测出一个部件
        if stats[1, 4] < 0.01 * n_total:  # 面积过小，排除
            return None, None

    # 定义新矩阵，用于存放指定部件的分割图
    out_segmap = np.zeros_like(segmap)
    out_points = []

    # 获取联通域面积
    comp_areas = stats[1:, 4]

    # 联通域面积从大到小排序
    area_sort_idx = np.argsort(-comp_areas)

    if comp_areas[area_sort_idx[0]] < 0.0001 * n_total:
        return None, None

    for aa in range(area_sort_idx.shape[0]):
        #
        if (comp_name == 'fanban' and aa > 1) or (comp_name == 'tianxian' and aa > 3) or (comp_name == 'xiangji' and aa > 0) \
                or (comp_name == 'benti' and aa > 0) or (comp_name == 'zaiwei' and aa > 5) or (comp_name == 'jixiebi' and aa > 0):
            break

        if comp_areas[area_sort_idx[aa]] < 0.0001 * n_total:
            continue

        points = np.argwhere(labels == area_sort_idx[aa]+1)  # [n_p, 2]
        # x y 互换
        points = points[:, ::-1]

        rect = cv2.minAreaRect(points)
        box = np.int32(cv2.boxPoints(rect))
        center = np.int32(rect[0])
        center = np.expand_dims(center, axis=0)
        size = rect[1]
        size = np.expand_dims(size, axis=0)

        out_one = np.concatenate([center, box, size], axis=0)
        out_points.append(out_one)

        out_segmap[labels == area_sort_idx[aa]+1] = comp_id

    out_points = np.array(out_points, dtype=np.int32)
    return out_segmap, out_points

def calRealSize(pixel_size, resolution):
    # 输入：部件像素尺寸(w, h)，相机分辨率(m/pixel)
    # 输出：部件物理尺寸
    real_size = pixel_size*resolution
    return real_size


# test post proess
# from dataloaders.utils import get_satellite_labels_6inst
# color_map = get_satellite_labels_6inst()
#
# def label2colormap(label):
#     types = np.int32(np.unique(label))
#     r, g, b = np.zeros_like(label), np.zeros_like(label), np.zeros_like(label)
#
#     for i in range(types.shape[0]):
#         r[label == types[i]] = color_map[types[i]][0]
#         g[label == types[i]] = color_map[types[i]][1]
#         b[label == types[i]] = color_map[types[i]][2]
#
#     rgb = np.dstack((r, g, b))
#     return rgb.astype('uint8')
#
# def draw_points(image, points):
#     for id, comps in points.items():
#         if comps is not None:
#             color = color_map[id]
#             color_cv = tuple([int(color[2]), int(color[1]), int(color[0])])
#             for i in range(comps.shape[0]):
#                 c = comps[i]
#                 center = c[0]
#                 rect = c[1:5]
#                 cv2.drawContours(image, [rect], 0, color_cv, 4)
#                 cv2.drawMarker(image, center, color_cv, markerType=0, thickness=4)
#     return image
#
# def save_results(base_save_path, sample_id, image, target, points):
#     image_ori = image
#     target = target
#
#     image_ori = image_ori.astype('uint8')
#
#     image_dp = np.stack([image_ori, image_ori, image_ori], axis=2)
#     image_dp = draw_points(image_dp, points)
#     image_dp = image_dp[:, :, ::-1]
#     image_dp_PIL = Image.fromarray(image_dp)
#     image_dp_PIL = image_dp_PIL.convert('RGBA')
#
#     image_ori_PIL = Image.fromarray(image_ori)
#     image_ori_PIL = image_ori_PIL.convert('RGBA')
#     w, h = image_ori_PIL.size
#
#     target_cm = label2colormap(target)
#
#     target_cm = Image.fromarray(target_cm)
#     target_cm = target_cm.convert('RGBA')
#
#     target_blend = Image.blend(image_ori_PIL, target_cm, 0.4)
#
#     results = Image.new('RGBA', (w*3, h))
#     results.paste(image_ori_PIL, (0, 0))
#     results.paste(target_blend, (w, 0))
#     results.paste(image_dp_PIL, (w*2, 0))
#
#     results.save(os.path.join(base_save_path, sample_id+'.png'))

# base_data_path = '/home/<USER>/projects/satellite_segmentation/train_200_models/dummy_data/src'
# base_label_path = '/home/<USER>/projects/satellite_segmentation/train_200_models/dummy_data/label'
#
# base_save_path = '/home/<USER>/projects/satellite_segmentation/train_200_models/test_post/vis'
# os.makedirs(base_save_path, exist_ok=True)
#
# data_names = os.listdir(base_data_path)
#
# for d_n in data_names:
#     d_p = os.path.join(base_data_path, d_n)
#     l_p = os.path.join(base_label_path, d_n)
#
#     data = Image.open(d_p).convert('L')
#     label = Image.open(l_p).convert('L')
#
#     data = np.array(data)
#     label = np.array(label)
#
#     print('data:{}, label:{}'.format(d_n, np.unique(label)))
#
#     out_segmap = np.zeros_like(label)
#
#     out_segmap1, out_points1 = componentFeature(label.copy(), 'fanban')
#     if out_segmap1 is not None:
#         out_segmap += out_segmap1
#
#     out_segmap2, out_points2 = componentFeature(label.copy(), 'tianxian')
#     if out_segmap2 is not None:
#         out_segmap += out_segmap2
#
#     out_segmap3, out_points3 = componentFeature(label.copy(), 'xiangji')
#     if out_segmap3 is not None:
#         out_segmap += out_segmap3
#
#     out_segmap4, out_points4 = componentFeature(label.copy(), 'benti')
#     if out_segmap4 is not None:
#         out_segmap += out_segmap4
#
#     out_points = {1: out_points1, 2: out_points2, 3:out_points3, 4: out_points4}
#
#     save_results(base_save_path, d_n, data, out_segmap, out_points)

