from matplotlib import colors
import mmcv
from matplotlib.collections import LineCollection
from matplotlib import pyplot as plt
from pose import project_3d_point
import numpy as np

# from mmcv import utils
#from mmcv.utils import is_str

EPS = 1e-2

def imshow_pose_axis(img,
                    radius,
                    rotations, 
                    translations,
                    labels,
                    camera_k,
                    scores=None,
                    class_names=None,
                    score_thr=0,
                    axis_color='green',
                    thickness=3,
                    win_name='',
                    wait_time=0,
                    show=True,
                    out_file=None):
    '''
    Visualize pose axis, determined by rotations, translations, and camera_k
    Args:
        img (str or np.ndarray): The image to be displayed.
        rotations (np.ndarray): Rotations, shape (n, 3, 3)
        translations (np.ndarray): Translations, shape (n, 3)
        labels (np.ndarray): Labels, shape (n)
        radius (list): Mesh diameter of each object
        camera_k (np.ndarray): Camera intinsic, shape (3, 3)
        scores (np.ndarray): Scores of each predicted element.
        class_names (list[str]): Names of each category.
        score_thr (float): Filter predictions by score threshold.
        axis_color (str or tuple(int) or list[str] or list[typle(int)]): Color of pose axis.
            Either a list for each class or a single element for all classes.
        win_name (str):
        wait_time (float):
        show (bool):
        out_file (str):
    '''
    if rotations.size == 0:
        return img
    img = mmcv.imread(img).astype(np.uint8)
    if score_thr > 0:
        assert scores is not None
        assert scores.shape[0] == rotations.shape[0] == translations.shape[0]
        inds = scores > score_thr
        rotations = rotations[inds]
        translations = translations[inds]
        labels = labels[inds]
        if camera_k.ndim > 3:
            camera_k = camera_k[inds]
    
    if isinstance(axis_color, list):
        axis_colors = [color_val_matplotlib(c) for c in axis_color]
    else:
        if class_names is not None:
            axis_colors = [color_val_matplotlib(axis_color)] * (len(class_names) + 1)
        else:
            axis_colors = [color_val_matplotlib(axis_color)] * (max(labels) + 1)
    
    img = mmcv.bgr2rgb(img)
    width, height = img.shape[1], img.shape[0]
    img = np.ascontiguousarray(img)

    fig = plt.figure(win_name, frameon=False)
    plt.title(win_name)
    canvas = fig.canvas
    dpi = fig.get_dpi()
    fig.set_size_inches((width + EPS)/dpi, (height + EPS)/dpi)

    plt.subplots_adjust(left=0, right=1, bottom=0, top=1)
    ax = plt.gca()
    ax.axis('off')


    plt.imshow(img)

    for i in range(len(rotations)):
        rotation, translation, label = rotations[i], translations[i], labels[i]
        k = camera_k if camera_k.ndim == 2 else camera_k[i]
        r = radius[label]
        points_3d = [(0, 0, 0), (0, 0, r), (0, r, 0), (r, 0, 0)]
        points_3d = np.array(points_3d).reshape(-1, 3)
        points_2d = project_3d_point(points_3d, k, rotation, translation)
        lines = []
        lines.append(np.stack([points_2d[0], points_2d[1]]))
        lines.append(np.stack([points_2d[0], points_2d[2]]))
        lines.append(np.stack([points_2d[0], points_2d[3]]))
        lines = np.stack(lines)
        line_collection = LineCollection(lines, 
                                        color = ['b', 'g', 'r'],
                                        linewidths=thickness)
        ax.add_collection(line_collection)
    
    # from matplotlib to cv2 
    stream, _ = canvas.print_to_buffer()
    buffer = np.frombuffer(stream, dtype='uint8')
    img_rgba = buffer.reshape(height, width, 4)
    rgb, alpha = np.split(img_rgba, [3], axis=2)
    img = rgb.astype(np.uint8)
    img = mmcv.rgb2bgr(img)

    if show:
        if wait_time == 0:
            plt.show()
        else:
            plt.show(block=False)
            plt.pause(wait_time)
    if out_file is not None:
        mmcv.imwrite(img, out_file)
    
    plt.close()
    return img

def color_val_matplotlib(color, RGB=True, normalize=True):
    """Convert various input in BGR order to normalized RGB matplotlib color
    tuples,

    Args:
        color (:obj:`Color`/str/tuple/int/ndarray): Color inputs

    Returns:
        tuple[float]: A tuple of 3 normalized floats indicating RGB channels.
    """
    color = color_val(color)
    if normalize:
        color = [color / 255 for color in color[::-1]]
    else:
        color = [color for color in color[::-1]]
    if not RGB:
        color.reverse()
    return tuple(color)


def color_val(color):
    #if mmcv.utils.is_str(color):
    if isinstance(color, str):
        # see https://matplotlib.org/stable/gallery/color/named_colors.html for full list of color names
        return tuple(map(lambda x:int(x*255), colors.to_rgba(color)[:3]))[::-1]
    elif isinstance(color, tuple):
        assert len(color) == 3
        for channel in color:
            assert 0 <= channel <= 255
        return color
    elif isinstance(color, int):
        assert 0 <= color <= 255
        return color, color, color
    elif isinstance(color, np.ndarray):
        assert color.ndim == 1 and color.size == 3
        assert np.all((color >= 0) & (color <= 255))
        color = color.astype(np.uint8)
        return tuple(color)
    else:
        raise TypeError(f'Invalid type for color: {type(color)}')


def draw_pose_result(img, R, T, out_file):
    radius = list([180.18])
    camera_params = np.array([716197.128, 0, 500, 0, 716197.128, 500, 0, 0, 1], dtype=np.float32).reshape(3, 3)

    R = R.reshape(1, 3, 3)
    T = T.reshape(1, 3)
    label = np.array([0])
    image = imshow_pose_axis(img=img, radius=radius, rotations=R, translations=T, labels=label, camera_k=camera_params,
                             out_file=out_file, show=False)

    return image